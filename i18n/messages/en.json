{"metadata": {"title": "Free Online CV Builder - Create Professional ATS-Friendly Resumes in Minutes | No Registration Required", "description": "Free online CV builder and resume maker. Create professional, ATS-friendly resumes with our intuitive drag-and-drop editor. Real-time preview, multiple themes, instant PDF export. No registration required, completely free to use.", "keywords": "Free CV Builder, Online Resume Builder, Professional CV Maker, ATS-Friendly Resume, Free Resume Builder, Online CV Creator, Resume Templates, PDF Resume, Job Application, Free Resume Maker, Professional Resume Builder, Online CV Builder, Free Resume Creator, ATS Resume Builder, Professional CV Creator", "pages": {"home": {"title": "Free Online CV Builder - Create Professional ATS-Friendly Resumes in Minutes | CV Builder Free", "description": "Build stunning professional resumes with our free online CV builder. ATS-friendly templates, real-time preview, instant PDF export. No registration required. Start creating your perfect resume today!", "keywords": "free cv builder, online resume builder, professional resume maker, ATS friendly resume, free resume templates, cv creator, resume generator, job application, career tools"}, "templates": {"title": "Professional CV Templates - 23+ Free Resume Templates | CV Builder Free", "description": "Choose from 23+ professional CV templates designed for different industries. Modern, classic, creative, and minimal designs. All templates are ATS-friendly and completely free to use.", "keywords": "cv templates, resume templates, professional resume designs, ATS friendly templates, free resume layouts, modern cv templates, creative resume templates, job application templates"}, "editor": {"title": "CV Editor - Build Your Professional Resume Online | CV Builder Free", "description": "Create and edit your professional resume with our intuitive online CV editor. Real-time preview, drag-and-drop interface, multiple sections, and instant PDF export. Completely free to use.", "keywords": "cv editor, resume editor, online resume builder, professional cv maker, resume creator, cv builder tool, free resume editor, job application builder"}, "blog": {"title": "Resume Tips & Career Advice - Expert Job Search Guidance | CV Builder Free", "description": "Get expert resume writing tips, career advice, and job search strategies. Learn how to create compelling resumes, ace interviews, and advance your career with our comprehensive guides.", "keywords": "resume tips, career advice, job search tips, resume writing guide, interview tips, career development, professional growth, job application advice"}, "signin": {"title": "Sign In - Access Your CV Builder Account | CV Builder Free", "description": "Sign in to your CV Builder Free account to access saved resumes, premium templates, and advanced features. Secure login with email or social media accounts.", "keywords": "sign in, login, cv builder account, resume builder login, user account, secure access"}}}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Resume Tips & Career Advice", "description": "Expert resume writing tips, career advice, and job search strategies to help you land your dream job", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the CV Builder experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about CV Builder?", "placeholder": "Leave your feedback here..."}, "cvbuilder": {"title": "CV Builder Free", "introTitle": "Free Professional AI Resume Builder", "introDesc": "Free all-in-one online editing, preview, PDF export, multilingual support, ATS-friendly templates. Completely free to use.", "startBtn": "Start Building Free Professional CV Now", "editingArea": "Free online editing area...", "previewArea": "Real-time preview area...", "add": "Add", "noWork": "No work experience yet.", "work": "Work Experience", "company": "Company", "position": "Position", "startDate": "Start Date", "startYear": "Start Year", "startMonth": "Start Month", "endDate": "End Date", "endYear": "End Year", "endMonth": "End Month", "description": "Description", "location": "Location", "education": "Education", "noEducation": "No education yet.", "school": "School", "degree": "Degree", "field": "Field", "skills": "Skills", "noSkills": "No skills yet.", "skillName": "Skill Name", "level": "Level", "profile": "Profile / Summary", "profilePlaceholder": "A brief summary about yourself...", "personalInfo": "Personal Information", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "avatar": "Avatar URL", "website": "Website", "linkedin": "LinkedIn", "github": "GitHub", "workSectionTitle": "Work Experience", "educationSectionTitle": "Education", "skillsSectionTitle": "Skills", "save": "Save", "load": "Load", "reset": "Reset to default!", "exportPDF": "Export PDF", "saved": "Saved to local storage!", "loaded": "Loaded from local storage!", "generatingPDF": "Generating PDF...", "exportedPDF": "Exported as PDF!", "exportPDFError": "PDF export failed. Please try again.", "pdfGenerationFailed": "PDF generation failed. Please try again.", "pdfGenerated": "PDF generated successfully!", "exportedJSON": "Exported as JSON!", "jsonExported": "JSON exported successfully!", "importedJSON": "Successfully imported from JSON!", "jsonImported": "JSON imported successfully!", "invalidFile": "Please select a valid JSON file!", "invalidJSON": "Invalid JSON file format!", "invalidJson": "Invalid JSON file!", "selectJsonFile": "Please select a JSON file!", "changeTheme": "Change Theme", "changeFont": "Change Font", "toggleSections": "Toggle Sections", "exportJSON": "Export as JSON", "importJSON": "Import from JSON", "import": "Import", "uploadAvatar": "Upload Avatar", "removeAvatar": "Remove", "invalidImageFile": "Please select a valid image file!", "imageTooLarge": "Image file is too large! Please select a file smaller than 5MB.", "remove": "Remove", "clickAddToStart": "Click the 'Add' button above to get started", "jobIntention": "Job Intention", "jobIntentionPlaceholder": "Describe your career goals and job preferences...", "projects": "Projects", "noProjects": "No projects yet.", "projectName": "Project Name", "projectUrl": "Project URL", "projectDescription": "Project Description", "projectTechnologies": "Technologies Used", "projectTechnologiesPlaceholder": "e.g. <PERSON><PERSON>, Node.js, MongoDB (separated by commas)", "projectAchievements": "Key Achievements", "projectAchievementsPlaceholder": "Describe the main achievements of this project...", "languages": "Languages", "noLanguages": "No languages yet.", "languageName": "Language", "proficiency": "Proficiency", "certifications": "Certifications", "noCertifications": "No certifications yet.", "certificationName": "Certification Name", "issuingOrganization": "Issuing Organization", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "certificationUrl": "Certification URL", "awards": "Awards & Honors", "noAwards": "No awards yet.", "awardName": "Award Name", "awardIssuer": "Issuing Organization", "awardDate": "Award Date", "awardDescription": "Award Description", "awardCategory": "Category", "interests": "Interests", "noInterests": "No interests yet.", "interestName": "Interest", "interestDescription": "Description", "volunteer": "Volunteer Experience", "noVolunteer": "No volunteer experience yet.", "volunteerOrganization": "Organization", "volunteerRole": "Role", "volunteerDescription": "Description", "publications": "Publications", "noPublications": "No publications yet.", "publicationTitle": "Publication Title", "publicationAuthors": "Authors", "publicationDate": "Publication Date", "publicationUrl": "Publication URL", "publicationDescription": "Description", "references": "References", "noReferences": "No references yet.", "referenceName": "Reference Name", "referenceTitle": "Title", "referenceCompany": "Company", "referenceEmail": "Email", "referencePhone": "Phone", "referenceDescription": "Description", "addItem": "Add Item", "delete": "Delete", "edit": "Edit", "cancel": "Cancel", "optional": "Optional", "required": "Required", "none": "None", "academic": "Academic", "professional": "Professional", "leadership": "Leadership", "innovation": "Innovation", "service": "Service", "competition": "Competition", "other": "Other", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "native": "Native", "fluent": "Fluent", "conversational": "Conversational", "basic": "Basic", "items": "items", "item": "item", "expectedPosition": "Expected Position", "expectedPositionPlaceholder": "e.g. <PERSON>end Developer, Product Manager...", "careerGoals": "Career Goals", "careerGoalsPlaceholder": "Describe your career goals and aspirations...", "preferredLocation": "Preferred Location", "preferredLocationPlaceholder": "e.g. New York, Remote, Anywhere...", "expectedSalary": "Expected <PERSON><PERSON>", "expectedSalaryPlaceholder": "e.g. $80,000 - $100,000", "targetSalary": "Target Salary", "availability": "Availability", "availabilityPlaceholder": "e.g. Immediately, 2 weeks notice...", "year": "Year", "month": "Month", "current": "Current", "present": "Present", "yourName": "Your Name", "beijingChina": "Beijing, China", "personalProfile": "Personal Profile", "workExperience": "Work Experience", "volunteerExperience": "Volunteer Experience"}, "save": "Save", "load": "Load", "reset": "Reset", "exportPDF": "Export PDF", "saved": "Saved to local storage!", "loaded": "Loaded from local storage!", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "skillName": "Skill Name", "level": "Level", "cvTemplate": {"title": "CV Template Center", "subtitle": "Choose a professional template to make your resume stand out", "backToEditor": "Back to Editor", "totalTemplates": "Premium Templates", "designStyles": "Design Styles", "layoutTypes": "Layout Types", "industryCoverage": "Industry Coverage", "filterTitle": "Filter Templates", "clearAll": "Clear All", "designStyle": "Design Style", "layoutType": "Layout Type", "targetIndustry": "Target Industry", "allStyles": "All Styles", "allLayouts": "All Layouts", "allIndustries": "All Industries", "selectedFilters": "Selected", "foundTemplates": "Found {count} templates", "showingAll": "Showing all templates", "filtersApplied": "Filters applied", "noTemplatesFound": "No templates found matching your criteria", "noTemplatesDesc": "Try adjusting your filters or view all templates", "clearFilters": "Clear Filters", "clickToPreview": "Click to preview template", "templateTaglines": {"default-cv-builder": "Complete showcase, fully professional", "modern-tech-two-column": "Tech-savvy design, highlighting technical expertise", "modern-startup-single": "Innovative thinking, rapid growth trajectory", "modern-product-grid": "Product mindset, project management expert", "classic-finance-single": "Professional rigor, finance industry preferred", "classic-law-two-column": "Authoritative professional, legal elite choice", "classic-corporate-timeline": "Career trajectory, corporate development witness", "minimal-academic-single": "Academic simplicity, research achievements highlighted", "minimal-international-grid": "Global perspective, overseas job hunting tool", "minimal-clean-timeline": "Clean and clear, suitable for all industries", "creative-designer-portfolio": "Unlimited creativity, portfolio showcase", "creative-artist-grid": "Artistic atmosphere, creative talent display", "creative-marketing-infographic": "Marketing creativity, data visualization", "elegant-luxury-single": "Luxury taste, premium service experience", "elegant-fashion-two-column": "Fashion elegance, lifestyle taste", "elegant-consulting-timeline": "Consulting expert, professional capability display", "tech-startup-infographic": "Tech innovation, entrepreneurial spirit embodiment", "modern-it-two-column": "Modern IT, technical professional display", "classic-law-single-column": "Legal authority, professional trust choice", "creative-designer-grid": "Design creativity, strong visual impact", "elegant-executive-single": "Executive style, leadership demonstration", "minimal-academic-timeline": "Academic trajectory, clear research journey"}}}