import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import Link from "next/link";
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { WebsiteJsonLd, WebApplicationJsonLd } from "@/components/seo/JsonLd";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  const logoUrl = 'https://image.generatepassword12.org/cvbuilderfreelogo.jpg';

  let canonicalUrl = baseUrl;
  if (locale !== "en") {
    canonicalUrl = `${baseUrl}/${locale}`;
  }

  // 获取首页特定的 metadata
  const homeMetadata = t.raw('metadata.pages.home') || {};
  const title = homeMetadata.title || t('metadata.title');
  const description = homeMetadata.description || t('metadata.description');
  const keywords = homeMetadata.keywords || t('metadata.keywords');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'CV Builder Free',
      locale: locale,
      images: [{
        url: logoUrl,
        width: 1200,
        height: 630,
        alt: 'CV Builder Free - Professional Resume Builder',
      }],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [logoUrl],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}/en`,
        'zh': `${baseUrl}/zh`,
      },
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.stats && <Stats section={page.stats} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}

      {/* SEO 结构化数据 */}
      <WebsiteJsonLd />
      <WebApplicationJsonLd />
    </>
  );
}
