import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import CVTemplateListPageClient from "./CVTemplateListPageClient";

// 生成SEO元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  const logoUrl = 'https://image.generatepassword12.org/cvbuilderfreelogo.jpg';

  let canonicalUrl = `${baseUrl}/cv-template`;
  if (locale !== 'en') {
    canonicalUrl = `${baseUrl}/${locale}/cv-template`;
  }

  // 获取模板页面特定的 metadata
  const templatesMetadata = t.raw('metadata.pages.templates') || {};
  const title = templatesMetadata.title || t('cvTemplate.title');
  const description = templatesMetadata.description || t('cvTemplate.subtitle');
  const keywords = templatesMetadata.keywords || (locale === 'zh' ?
    '简历模板,CV模板,简历制作,PDF导出,专业简历,免费模板,在线简历,求职简历' :
    'cv templates,resume templates,professional resume,free templates,online cv,job application');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'CV Builder Free',
      locale: locale,
      images: [{
        url: logoUrl,
        width: 1200,
        height: 630,
        alt: 'Professional CV Templates - CV Builder Free',
      }],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [logoUrl],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}/cv-template`,
        'zh': `${baseUrl}/zh/cv-template`,
      },
    },
    openGraph: {
      title: `${t('cvTemplate.title')} - CV Builder Free`,
      description: t('cvTemplate.subtitle'),
      url: canonicalUrl,
      siteName: 'CV Builder Free',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${t('cvTemplate.title')} - CV Builder Free`,
      description: t('cvTemplate.subtitle'),
    },
  };
}

export default function CVTemplateListPage() {
  return <CVTemplateListPageClient />;
}