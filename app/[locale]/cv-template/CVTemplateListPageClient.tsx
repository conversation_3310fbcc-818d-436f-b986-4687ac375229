"use client";
import { useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import TemplateCard from "@/components/cv-template/TemplateCard";
import FilterBar from "@/components/cv-template/FilterBar";
import { TEMPLATES } from "@/components/cv-template/templates/meta";
import Header from "@/components/blocks/header";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import StructuredData from "@/components/seo/StructuredData";
import FriendlyLinks from "@/components/blocks/friendly-links";

export default function CVTemplateListPageClient() {
  const t = useTranslations();
  const locale = useLocale();
  const [filters, setFilters] = useState({ style: "", layout: "", industry: "" });
  
  // 根据当前语言和筛选条件过滤模板
  const filtered = TEMPLATES.filter(template => {
    const templateStyle = locale === 'en' && template.styleEn ? template.styleEn : template.style;
    const templateLayout = locale === 'en' && template.layoutEn ? template.layoutEn : template.layout;
    const templateIndustry = locale === 'en' && template.industryEn ? template.industryEn : template.industry;
    
    return (!filters.style || templateStyle === filters.style) &&
           (!filters.layout || templateLayout === filters.layout) &&
           (!filters.industry || templateIndustry === filters.industry);
  });

  const headerConfig = {
    brand: {
      title: t('cvbuilder.title', { default: 'CV Builder Free' }),
      logo: { src: "https://image.generatepassword12.org/cvbuilderfreelogo.jpg", alt: "CV Builder Free" },
      url: "/"
    },
    show_locale: true,
    show_theme: true,
  };

  return (
    <>
      <StructuredData
        type="webpage"
        data={{
          name: t('cvTemplate.title'),
          url: `${process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com'}/cv-template`,
          description: t('cvTemplate.subtitle'),
        }}
      />
      <Header header={headerConfig} />
      <main className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto py-8">
          <div className="max-w-7xl mx-auto p-4">
            {/* 返回按钮和标题 */}
            <div className="flex items-center gap-4 mb-8">
              <Link href="/cv-template/default-cv-builder">
                <Button variant="outline" size="sm" className="flex items-center gap-2 hover:bg-blue-50">
                  <ArrowLeft size={16} />
                  {t('cvTemplate.backToEditor')}
                </Button>
              </Link>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {t('cvTemplate.title')}
                </h1>
                <p className="text-gray-600 mt-2">{t('cvTemplate.subtitle')}</p>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{TEMPLATES.length}</div>
                  <div className="text-sm text-gray-500">{t('cvTemplate.totalTemplates')}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">6</div>
                  <div className="text-sm text-gray-500">{t('cvTemplate.designStyles')}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">7</div>
                  <div className="text-sm text-gray-500">{t('cvTemplate.layoutTypes')}</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">15+</div>
                  <div className="text-sm text-gray-500">{t('cvTemplate.industryCoverage')}</div>
                </div>
              </div>
            </div>

            <FilterBar filters={filters} setFilters={setFilters} />

            {/* 模板网格 */}
            <div className="mt-8">
              {filtered.length > 0 ? (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-800">
                      {t('cvTemplate.foundTemplates', { count: filtered.length })}
                    </h2>
                    <div className="text-sm text-gray-500">
                      {filters.style || filters.layout || filters.industry ? t('cvTemplate.filtersApplied') : t('cvTemplate.showingAll')}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filtered.map(template => (
                      <TemplateCard key={template.name} template={template} />
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-16">
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 max-w-md mx-auto">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <ArrowLeft size={24} className="text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">{t('cvTemplate.noTemplatesFound')}</h3>
                    <p className="text-gray-500 mb-6">{t('cvTemplate.noTemplatesDesc')}</p>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setFilters({ style: "", layout: "", industry: "" })}
                    >
                      {t('cvTemplate.clearFilters')}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <FriendlyLinks />
    </>
  );
}
