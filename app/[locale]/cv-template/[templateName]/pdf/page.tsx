"use client";
import dynamic from "next/dynamic";
import { notFound, useSearchParams } from "next/navigation";
import { TEMPLATES } from "@/components/cv-template/templates/meta";
import { CVBuilderProvider, useCVBuilder } from "@/contexts/cv-builder";
import { use, useEffect } from "react";
import "@/styles/pdf-export.css";

const templateMap: Record<string, any> = {
  // CV-Builder编辑器默认模板
  "default-cv-builder": dynamic(() => import("@/components/cv-template/templates/DefaultCVBuilder"), { ssr: false }),
  
  // 现代风格组合
  "modern-tech-two-column": dynamic(() => import("@/components/cv-template/templates/ModernTechTwoColumn"), { ssr: false }),
  "modern-startup-single": dynamic(() => import("@/components/cv-template/templates/ModernStartupSingle"), { ssr: false }),
  "modern-product-grid": dynamic(() => import("@/components/cv-template/templates/ModernProductGrid"), { ssr: false }),
  
  // 经典风格组合
  "classic-finance-single": dynamic(() => import("@/components/cv-template/templates/ClassicFinanceSingle"), { ssr: false }),
  "classic-law-two-column": dynamic(() => import("@/components/cv-template/templates/ClassicLawTwoColumn"), { ssr: false }),
  "classic-corporate-timeline": dynamic(() => import("@/components/cv-template/templates/ClassicCorporateTimeline"), { ssr: false }),
  
  // 极简风格组合
  "minimal-academic-single": dynamic(() => import("@/components/cv-template/templates/MinimalAcademicSingle"), { ssr: false }),
  "minimal-international-grid": dynamic(() => import("@/components/cv-template/templates/MinimalInternationalGrid"), { ssr: false }),
  "minimal-clean-timeline": dynamic(() => import("@/components/cv-template/templates/MinimalCleanTimeline"), { ssr: false }),
  
  // 创意风格组合
  "creative-designer-portfolio": dynamic(() => import("@/components/cv-template/templates/CreativeDesignerPortfolio"), { ssr: false }),
  "creative-artist-grid": dynamic(() => import("@/components/cv-template/templates/CreativeArtistGrid"), { ssr: false }),
  "creative-marketing-infographic": dynamic(() => import("@/components/cv-template/templates/CreativeMarketingInfographic"), { ssr: false }),
  
  // 优雅风格组合
  "elegant-luxury-single": dynamic(() => import("@/components/cv-template/templates/ElegantLuxurySingle"), { ssr: false }),
  "elegant-fashion-two-column": dynamic(() => import("@/components/cv-template/templates/ElegantFashionTwoColumn"), { ssr: false }),
  "elegant-consulting-timeline": dynamic(() => import("@/components/cv-template/templates/ElegantConsultingTimeline"), { ssr: false }),
  
  // 科技感风格组合
  "tech-startup-infographic": dynamic(() => import("@/components/cv-template/templates/TechStartupInfographic"), { ssr: false }),
  "modern-it-two-column": dynamic(() => import("@/components/cv-template/templates/ModernITTwoColumn"), { ssr: false }),
  "classic-law-single-column": dynamic(() => import("@/components/cv-template/templates/ClassicLawSingleColumn"), { ssr: false }),
  "creative-designer-grid": dynamic(() => import("@/components/cv-template/templates/CreativeDesignerGrid"), { ssr: false }),
  "elegant-executive-single": dynamic(() => import("@/components/cv-template/templates/ElegantExecutiveSingle"), { ssr: false }),
  "minimal-academic-timeline": dynamic(() => import("@/components/cv-template/templates/MinimalAcademicTimeline"), { ssr: false }),
};

function PDFPreviewInner({ templateName }: { templateName: string }) {
  const TemplatePreview = templateMap[templateName];
  const meta = TEMPLATES.find(t => t.name === templateName);
  const { data, setData } = useCVBuilder();
  const searchParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null;

  useEffect(() => {
    if (searchParams && searchParams.get('print') === '1') {
      setTimeout(() => window.print(), 500);
    }
  }, []);

  if (!TemplatePreview || !meta) return notFound();

  useEffect(() => {
    // 监听来自 Puppeteer 的数据更新事件
    const handleDataUpdate = (event: CustomEvent) => {
      if (event.detail) {
        setData(event.detail);
      }
    };

    window.addEventListener('updateCVData', handleDataUpdate as EventListener);
    
    return () => {
      window.removeEventListener('updateCVData', handleDataUpdate as EventListener);
    };
  }, [setData]);

  return (
    <div>
      <div className="print:hidden bg-yellow-50 border-b border-yellow-200 text-yellow-800 text-center py-2 text-sm">
        按 Ctrl+P (或 ⌘+P) 打开浏览器打印，选择"另存为PDF"即可导出高清简历。建议使用 Chrome 浏览器。<br />
        <span className="text-xs text-gray-500">如未自动弹出打印，请手动按 Ctrl+P / ⌘+P</span>
      </div>
      <div className="pdf-container" id="cv-preview">
        <TemplatePreview data={data} isExport={true} />
      </div>
    </div>
  );
}

export default function PDFPreviewPage({ params }: { params: Promise<{ templateName: string }> }) {
  const { templateName } = use(params);
  
  return (
    <CVBuilderProvider>
      <PDFPreviewInner templateName={templateName} />
    </CVBuilderProvider>
  );
}
