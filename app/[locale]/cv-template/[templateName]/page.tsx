import { TEMPLATES } from "@/components/cv-template/templates/meta";
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import TemplateEditPageClient from "./TemplateEditPageClient";
import { TemplateJsonLd, BreadcrumbJsonLd } from "@/components/seo/JsonLd";

// 生成SEO元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; templateName: string }>;
}): Promise<Metadata> {
  const { locale, templateName } = await params;
  const t = await getTranslations();
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  const logoUrl = 'https://image.generatepassword12.org/cvbuilderfreelogo.jpg';

  const template = TEMPLATES.find(tmpl => tmpl.name === templateName);

  if (!template) {
    return {
      title: 'Template Not Found - CV Builder Free',
      description: 'The requested CV template was not found. Browse our collection of professional resume templates.',
      keywords: 'cv template, resume template, not found',
    };
  }

  const templateTitle = locale === 'en' && template.titleEn ? template.titleEn : template.title;
  const templateDesc = locale === 'en' && template.descriptionEn ? template.descriptionEn : template.description;
  const industry = locale === 'en' && template.industryEn ? template.industryEn : template.industry;
  const style = locale === 'en' && template.styleEn ? template.styleEn : template.style;

  let canonicalUrl = `${baseUrl}/cv-template/${templateName}`;
  if (locale !== 'en') {
    canonicalUrl = `${baseUrl}/${locale}/cv-template/${templateName}`;
  }

  const title = locale === 'zh' ?
    `${templateTitle} - 专业简历模板 | CV Builder Free` :
    `${templateTitle} - Professional CV Template | CV Builder Free`;

  const description = locale === 'zh' ?
    `使用${templateTitle}模板创建专业简历。${templateDesc} 支持实时预览、PDF导出，完全免费使用。` :
    `Create a professional resume with our ${templateTitle} template. ${templateDesc} Features real-time preview and PDF export. Completely free to use.`;

  const keywords = locale === 'zh' ?
    `简历模板,CV模板,${templateTitle},简历制作,PDF导出,${industry},${style}风格,免费简历模板` :
    `cv template,resume template,${templateTitle.toLowerCase()},professional resume,PDF export,${industry.toLowerCase()},${style.toLowerCase()} style,free resume template`;

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'CV Builder Free',
      locale: locale,
      images: [{
        url: logoUrl,
        width: 1200,
        height: 630,
        alt: `${templateTitle} CV Template Preview`,
      }],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [logoUrl],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}/cv-template/${templateName}`,
        'zh': `${baseUrl}/zh/cv-template/${templateName}`,
      },
    },
  };
}

export default async function TemplateEditPage({
  params
}: {
  params: Promise<{ locale: string; templateName: string }>
}) {
  const { locale, templateName } = await params;
  const template = TEMPLATES.find(tmpl => tmpl.name === templateName);

  if (!template) {
    return <TemplateEditPageClient params={params} />;
  }

  const templateTitle = locale === 'en' && template.titleEn ? template.titleEn : template.title;
  const templateDesc = locale === 'en' && template.descriptionEn ? template.descriptionEn : template.description;
  const industry = locale === 'en' && template.industryEn ? template.industryEn : template.industry;
  const style = locale === 'en' && template.styleEn ? template.styleEn : template.style;
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';

  const breadcrumbItems = [
    { name: locale === 'zh' ? '首页' : 'Home', url: baseUrl },
    { name: locale === 'zh' ? '简历模板' : 'CV Templates', url: `${baseUrl}/cv-template` },
    { name: templateTitle, url: `${baseUrl}/cv-template/${templateName}` }
  ];

  return (
    <>
      <TemplateEditPageClient params={params} />

      {/* SEO 结构化数据 */}
      <TemplateJsonLd
        templateName={templateName}
        title={templateTitle}
        description={templateDesc}
        industry={industry}
        style={style}
      />
      <BreadcrumbJsonLd items={breadcrumbItems} />
    </>
  );
}