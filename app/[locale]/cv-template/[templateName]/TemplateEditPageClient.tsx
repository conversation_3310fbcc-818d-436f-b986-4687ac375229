"use client";
import dynamic from "next/dynamic";
import { notFound } from "next/navigation";
import { TEMPLATES } from "@/components/cv-template/templates/meta";
import EditorLayout from "@/components/cv-builder/EditorLayout";
import { CVBuilderProvider, useCVBuilder } from "@/contexts/cv-builder";
import TopNavbar from "@/components/cv-template/TopNavbar";
import { useTranslations, useLocale } from "next-intl";
import { use } from "react";

const templateMap: Record<string, any> = {
  // CV-Builder编辑器默认模板
  "default-cv-builder": dynamic(() => import("@/components/cv-template/templates/DefaultCVBuilder"), { ssr: false }),
  
  // 现代风格组合
  "modern-tech-two-column": dynamic(() => import("@/components/cv-template/templates/ModernTechTwoColumn"), { ssr: false }),
  "modern-startup-single": dynamic(() => import("@/components/cv-template/templates/ModernStartupSingle"), { ssr: false }),
  "modern-product-grid": dynamic(() => import("@/components/cv-template/templates/ModernProductGrid"), { ssr: false }),
  
  // 经典风格组合
  "classic-finance-single": dynamic(() => import("@/components/cv-template/templates/ClassicFinanceSingle"), { ssr: false }),
  "classic-law-two-column": dynamic(() => import("@/components/cv-template/templates/ClassicLawTwoColumn"), { ssr: false }),
  "classic-corporate-timeline": dynamic(() => import("@/components/cv-template/templates/ClassicCorporateTimeline"), { ssr: false }),
  
  // 极简风格组合
  "minimal-academic-single": dynamic(() => import("@/components/cv-template/templates/MinimalAcademicSingle"), { ssr: false }),
  "minimal-international-grid": dynamic(() => import("@/components/cv-template/templates/MinimalInternationalGrid"), { ssr: false }),
  "minimal-clean-timeline": dynamic(() => import("@/components/cv-template/templates/MinimalCleanTimeline"), { ssr: false }),
  
  // 创意风格组合
  "creative-designer-portfolio": dynamic(() => import("@/components/cv-template/templates/CreativeDesignerPortfolio"), { ssr: false }),
  "creative-artist-grid": dynamic(() => import("@/components/cv-template/templates/CreativeArtistGrid"), { ssr: false }),
  "creative-marketing-infographic": dynamic(() => import("@/components/cv-template/templates/CreativeMarketingInfographic"), { ssr: false }),
  
  // 优雅风格组合
  "elegant-luxury-single": dynamic(() => import("@/components/cv-template/templates/ElegantLuxurySingle"), { ssr: false }),
  "elegant-fashion-two-column": dynamic(() => import("@/components/cv-template/templates/ElegantFashionTwoColumn"), { ssr: false }),
  "elegant-consulting-timeline": dynamic(() => import("@/components/cv-template/templates/ElegantConsultingTimeline"), { ssr: false }),
  
  // 科技感风格组合
  "tech-startup-infographic": dynamic(() => import("@/components/cv-template/templates/TechStartupInfographic"), { ssr: false }),
  "modern-it-two-column": dynamic(() => import("@/components/cv-template/templates/ModernITTwoColumn"), { ssr: false }),
  "classic-law-single-column": dynamic(() => import("@/components/cv-template/templates/ClassicLawSingleColumn"), { ssr: false }),
  "creative-designer-grid": dynamic(() => import("@/components/cv-template/templates/CreativeDesignerGrid"), { ssr: false }),
  "elegant-executive-single": dynamic(() => import("@/components/cv-template/templates/ElegantExecutiveSingle"), { ssr: false }),
  "minimal-academic-timeline": dynamic(() => import("@/components/cv-template/templates/MinimalAcademicTimeline"), { ssr: false }),
};

function TemplateEditPageInner({ templateName }: { templateName: string }) {
  const TemplatePreview = templateMap[templateName];
  const meta = TEMPLATES.find(tmpl => tmpl.name === templateName);
  const { data } = useCVBuilder();
  const t = useTranslations();
  const locale = useLocale();

  // 根据当前语言选择模板标题
  const getLocalizedTitle = (template: any) => {
    return locale === 'en' && template.titleEn ? template.titleEn : template.title;
  };

  if (!TemplatePreview || !meta) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavbar templateName={templateName} templateTitle={getLocalizedTitle(meta)} />
      <div className="h-[calc(100vh-4rem)]">
        <EditorLayout
          hideToolbar={true}
          isFullScreen={true}
          previewComponent={<TemplatePreview data={data} isExport={false} />}
        />
      </div>
    </div>
  );
}

export default function TemplateEditPageClient({ params }: { params: Promise<{ templateName: string }> }) {
  const { templateName } = use(params);

  return (
    <CVBuilderProvider>
      <TemplateEditPageInner templateName={templateName} />
    </CVBuilderProvider>
  );
}
