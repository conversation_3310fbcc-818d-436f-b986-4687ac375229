import DashboardLayout from "@/components/dashboard/layout";
import Empty from "@/components/blocks/empty";
import { ReactNode } from "react";
import { Sidebar } from "@/types/blocks/sidebar";
import { getUserInfo } from "@/services/user";
import { redirect } from "next/navigation";
import { auth } from "@/auth";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  // 调试 session 信息
  const session = await auth();
  console.log("Session info:", session?.user?.email);

  const userInfo = await getUserInfo();
  console.log("User info from database:", userInfo);

  if (!userInfo || !userInfo.email) {
    console.log("No user info, redirecting to signin");
    redirect("/auth/signin");
  }

  const adminEmails = process.env.ADMIN_EMAILS?.split(",");

  // 调试信息
  console.log("Admin access check:");
  console.log("User email:", userInfo?.email);
  console.log("Admin emails:", adminEmails);
  console.log("Has access:", adminEmails?.includes(userInfo?.email));

  if (!adminEmails?.includes(userInfo?.email)) {
    return <Empty message={`No access. Your email: ${userInfo?.email}`} />;
  }

  const sidebar: Sidebar = {
    brand: {
      title: "ShipAny",
      logo: {
        src: "https://image.generatepassword12.org/cvbuilderfreelogo.jpg",
        alt: "ShipAny",
      },
      url: "/admin",
    },
    nav: {
      items: [
        {
          title: "Users",
          url: "/admin/users",
          icon: "RiUserLine",
        },
        {
          title: "Orders",
          icon: "RiOrderPlayLine",
          is_expand: true,
          children: [
            {
              title: "Paid Orders",
              url: "/admin/paid-orders",
            },
          ],
        },
        {
          title: "Posts",
          url: "/admin/posts",
          icon: "RiArticleLine",
        },
        {
          title: "Feedbacks",
          url: "/admin/feedbacks",
          icon: "RiMessage2Line",
        },
      ],
    },
    social: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_blank",
          icon: "RiHomeLine",
        },
        {
          title: "Github",
          url: "https://github.com/shipanyai/shipany-template-one",
          target: "_blank",
          icon: "RiGithubLine",
        },
        {
          title: "Discord",
          url: "https://discord.gg/HQNnrzjZQS",
          target: "_blank",
          icon: "RiDiscordLine",
        },
        {
          title: "X",
          url: "https://x.com/shipanyai",
          target: "_blank",
          icon: "RiTwitterLine",
        },
      ],
    },
  };

  return <DashboardLayout sidebar={sidebar}>{children}</DashboardLayout>;
}
