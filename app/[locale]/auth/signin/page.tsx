import SignForm from "@/components/sign/form";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  const logoUrl = 'https://image.generatepassword12.org/cvbuilderfreelogo.jpg';

  let canonicalUrl = `${baseUrl}/auth/signin`;
  if (locale !== 'en') {
    canonicalUrl = `${baseUrl}/${locale}/auth/signin`;
  }

  // 获取登录页面特定的 metadata
  const signinMetadata = t.raw('metadata.pages.signin') || {};
  const title = signinMetadata.title || (locale === 'zh' ? '登录 - CV Builder Free' : 'Sign In - CV Builder Free');
  const description = signinMetadata.description || (locale === 'zh' ?
    '登录您的CV Builder Free账户以访问保存的简历、高级模板和高级功能。' :
    'Sign in to your CV Builder Free account to access saved resumes, premium templates, and advanced features.');
  const keywords = signinMetadata.keywords || (locale === 'zh' ?
    '登录,用户登录,简历制作账户,简历制作工具登录,用户账户,安全访问' :
    'sign in,login,cv builder account,resume builder login,user account,secure access');

  return {
    title,
    description,
    keywords,
    robots: {
      index: false,
      follow: true,
    },
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function SignInPage({
  searchParams,
}: {
  searchParams: Promise<{ callbackUrl: string | undefined }>;
}) {
  const { callbackUrl } = await searchParams;
  const session = await auth();
  if (session) {
    return redirect(callbackUrl || "/");
  }

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <a href="/" className="flex items-center gap-2 self-center font-medium">
          <div className="flex h-6 w-6 items-center justify-center rounded-md border text-primary-foreground">
            <img src="https://image.generatepassword12.org/cvbuilderfreelogo.jpg" alt="logo" className="size-4" />
          </div>
          {process.env.NEXT_PUBLIC_PROJECT_NAME}
        </a>
        <SignForm />
      </div>
    </div>
  );
}
