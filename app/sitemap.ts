import { MetadataRoute } from 'next'
import { TEMPLATES } from '@/components/cv-template/templates/meta'

const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com'

/**
 * 生成优化的 sitemap.xml
 * 包含所有重要页面，支持多语言，设置合理的优先级和更新频率
 *
 * 优先级说明：
 * 1.0 - 首页（最重要）
 * 0.95 - CV Builder 编辑器（核心功能）
 * 0.9 - 模板中心页面
 * 0.85 - 热门模板（现代风格、IT行业、通用）
 * 0.8 - 其他模板
 * 0.7 - 博客和分类页面
 * 0.6 - 用户功能页面
 * 0.3 - 法律页面
 */
export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date().toISOString()

  // 基础页面
  const staticPages: MetadataRoute.Sitemap = [
    // 首页 - 最高优先级
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/en`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/zh`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    },

    // CV Builder 编辑器页面 - 最高优先级核心功能
    {
      url: `${baseUrl}/cv-template/default-cv-builder`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/en/cv-template/default-cv-builder`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.95,
    },
    {
      url: `${baseUrl}/zh/cv-template/default-cv-builder`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.95,
    },

    // 模板中心页面 - 高优先级
    {
      url: `${baseUrl}/cv-template`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/en/cv-template`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/zh/cv-template`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },

    // 博客页面
    {
      url: `${baseUrl}/posts`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/en/posts`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/zh/posts`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },

    // 用户功能页面
    {
      url: `${baseUrl}/auth/signin`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/en/auth/signin`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/zh/auth/signin`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },

    // 法律页面 - 低优先级
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms-of-service`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.3,
    },
  ]

  // 动态生成所有CV模板页面
  const templatePages: MetadataRoute.Sitemap = []

  TEMPLATES.forEach(template => {
    // 跳过默认编辑器模板，因为已经在静态页面中单独处理
    if (template.name === 'default-cv-builder') {
      return
    }

    // 根据模板类型设置不同的优先级
    let priority = 0.8

    // 现代风格模板优先级更高
    if (template.style === '现代' || template.styleEn === 'Modern') {
      priority = 0.85
    }

    // IT/开发行业模板优先级更高
    if (template.industry === 'IT/开发' || template.industryEn === 'IT/Development') {
      priority = 0.85
    }

    // 通用模板优先级更高
    if (template.industry === '通用' || template.industryEn === 'General') {
      priority = 0.85
    }

    // 英文模板页面
    templatePages.push({
      url: `${baseUrl}/cv-template/${template.name}`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: priority,
    })

    // 英文明确路径
    templatePages.push({
      url: `${baseUrl}/en/cv-template/${template.name}`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: priority,
    })

    // 中文模板页面
    templatePages.push({
      url: `${baseUrl}/zh/cv-template/${template.name}`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: priority,
    })
  })

  // 添加分类页面和其他重要页面
  const categoryPages: MetadataRoute.Sitemap = [
    // 按风格分类
    {
      url: `${baseUrl}/cv-template?style=modern`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/en/cv-template?style=modern`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/zh/cv-template?style=modern`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },

    // 按行业分类
    {
      url: `${baseUrl}/cv-template?industry=it`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/en/cv-template?industry=it`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/zh/cv-template?industry=it`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },

    // 按布局分类
    {
      url: `${baseUrl}/cv-template?layout=two-column`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/en/cv-template?layout=two-column`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/zh/cv-template?layout=two-column`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.6,
    },
  ]

  // 按模板数量排序，确保最重要的页面在前面
  const allPages = [...staticPages, ...templatePages, ...categoryPages]

  // 按优先级排序（高优先级在前）
  allPages.sort((a, b) => (b.priority || 0) - (a.priority || 0))

  return allPages
}
