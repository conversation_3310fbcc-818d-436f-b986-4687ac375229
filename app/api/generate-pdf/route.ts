import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import chromium from '@sparticuz/chromium';

export async function POST(request: NextRequest) {
  try {
    const { templateUrl, data } = await request.json();

    console.log('PDF generation request:', { templateUrl, dataKeys: Object.keys(data || {}) });

    if (!templateUrl || !data) {
      console.error('Missing required parameters:', { templateUrl: !!templateUrl, data: !!data });
      return NextResponse.json(
        { error: 'Template URL and data are required' },
        { status: 400 }
      );
    }

    // 获取基础URL
    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3001';

    // 确保templateUrl是完整的URL
    const fullUrl = templateUrl.startsWith('http')
      ? templateUrl
      : `${baseUrl}${templateUrl.startsWith('/') ? templateUrl : '/' + templateUrl}`;

    console.log('Full URL for PDF generation:', fullUrl);

    let browser;
    try {
      // 配置 Puppeteer 以在 Vercel 环境中运行
      const isProduction = process.env.NODE_ENV === 'production';

      browser = await puppeteer.launch({
        args: isProduction ? chromium.args : [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--font-render-hinting=none',
          '--disable-font-subpixel-positioning',
          '--enable-font-antialiasing'
        ],
        defaultViewport: { width: 794, height: 1123 },
        executablePath: isProduction ? await chromium.executablePath() : undefined,
        headless: true,
        ignoreHTTPSErrors: true,
      } as any);

      const page = await browser.newPage();

      // 设置视口，确保页面正确渲染
      await page.setViewport({ width: 794, height: 1123 }); // A4 尺寸

      // 导航到PDF预览页面
      await page.goto(fullUrl, {
        waitUntil: 'networkidle0',
        timeout: 90000 // 增加到90秒
      });

      console.log('Page loaded, waiting for content to render...');

      // 等待页面内容渲染完成
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查页面是否正常加载
      const pageTitle = await page.title();
      console.log('Page title:', pageTitle);

      // 等待 #cv-preview 元素出现
      try {
        await page.waitForSelector('#cv-preview', {
          visible: true,
          timeout: 20000
        });
        console.log('#cv-preview element found and visible');

        // 等待内容完全渲染
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.log('Failed to find #cv-preview element:', error);
        console.log('Page HTML snippet:', (await page.content()).substring(0, 1000));
        throw new Error('#cv-preview element not found or not visible');
      }

      // 设置字体，确保中文字体支持
      await page.evaluate(() => {
        const style = document.createElement('style');
        style.textContent = `
          * {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "SimSun", "宋体", sans-serif !important;
          }
        `;
        document.head.appendChild(style);
      });

      // 注入数据到页面
      await page.evaluate((cvData) => {
        // 将数据存储到localStorage
        localStorage.setItem('cvbuilder.data', JSON.stringify(cvData));
        
        // 触发数据更新事件
        window.dispatchEvent(new CustomEvent('updateCVData', { detail: cvData }));
      }, data);

      // 等待数据更新完成和页面重新渲染，增加等待时间
      await new Promise(resolve => setTimeout(resolve, 8000));

      // 注入优化的 PDF 样式
      await page.addStyleTag({
        content: `
          @page {
            size: 794px 1123px;
            margin: 56px;
          }
          html, body {
            width: 794px !important;
            height: 1123px !important;
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "SimSun", "宋体", sans-serif !important;
          }
          .pdf-container {
            width: 100% !important;
            max-width: 100% !important;
            min-height: 100% !important;
            margin: 0 !important;
            box-sizing: border-box !important;
            background: white !important;
            padding: 20px !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
            color: #000 !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "SimSun", "宋体", sans-serif !important;
          }
          .pdf-two-column {
            display: flex !important;
            width: 100% !important;
            min-height: 0 !important;
            gap: 20px !important;
          }
          .pdf-sidebar {
            width: 240px !important;
            min-width: 240px !important;
            max-width: 240px !important;
            page-break-inside: avoid !important;
            flex-shrink: 0 !important;
          }
          .pdf-main-content {
            flex: 1 !important;
            min-width: 0 !important;
            page-break-inside: avoid !important;
          }
          .pdf-section, .pdf-item {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
            margin-bottom: 12px !important;
          }
          .pdf-section:last-child, .pdf-item:last-child {
            margin-bottom: 0 !important;
          }
          /* 文字大小优化 */
          .pdf-container h1 {
            font-size: 18px !important;
            line-height: 1.2 !important;
            margin-bottom: 8px !important;
            font-weight: bold !important;
          }
          .pdf-container h2 {
            font-size: 16px !important;
            line-height: 1.3 !important;
            margin-bottom: 6px !important;
            font-weight: 600 !important;
          }
          .pdf-container h3 {
            font-size: 14px !important;
            line-height: 1.3 !important;
            margin-bottom: 4px !important;
            font-weight: 600 !important;
          }
          .pdf-container p, .pdf-container div {
            font-size: 12px !important;
            line-height: 1.4 !important;
            margin-bottom: 4px !important;
          }
          .pdf-container .text-xs {
            font-size: 10px !important;
            line-height: 1.2 !important;
          }
          .pdf-container .text-sm {
            font-size: 11px !important;
            line-height: 1.3 !important;
          }
          .pdf-container .text-base {
            font-size: 12px !important;
            line-height: 1.4 !important;
          }
          .pdf-container .text-lg {
            font-size: 14px !important;
            line-height: 1.3 !important;
          }
          .pdf-container .text-xl {
            font-size: 16px !important;
            line-height: 1.2 !important;
          }
          .pdf-container .text-2xl {
            font-size: 18px !important;
            line-height: 1.1 !important;
          }
          .pdf-container .text-3xl {
            font-size: 20px !important;
            line-height: 1.1 !important;
          }
          /* 间距优化 */
          .pdf-container .mb-1 { margin-bottom: 4px !important; }
          .pdf-container .mb-2 { margin-bottom: 8px !important; }
          .pdf-container .mb-3 { margin-bottom: 12px !important; }
          .pdf-container .mb-4 { margin-bottom: 16px !important; }
          .pdf-container .mb-6 { margin-bottom: 24px !important; }
          .pdf-container .mb-8 { margin-bottom: 32px !important; }
          .pdf-container .space-y-1 > * + * { margin-top: 4px !important; }
          .pdf-container .space-y-2 > * + * { margin-top: 8px !important; }
          .pdf-container .space-y-3 > * + * { margin-top: 12px !important; }
          .pdf-container .space-y-4 > * + * { margin-top: 16px !important; }
          .pdf-container .space-y-6 > * + * { margin-top: 24px !important; }
          /* 网格布局优化 */
          .pdf-container .grid {
            display: grid !important;
          }
          .pdf-container .grid-cols-1 {
            grid-template-columns: 1fr !important;
          }
          .pdf-container .lg\\:grid-cols-3 {
            grid-template-columns: 1fr 1fr 1fr !important;
          }
          .pdf-container .lg\\:col-span-2 {
            grid-column: span 2 !important;
          }
          .pdf-container .gap-6 {
            gap: 24px !important;
          }
          .pdf-container .gap-8 {
            gap: 32px !important;
          }
          /* 边框和背景 */
          .pdf-container .border-l-4 {
            border-left-width: 4px !important;
            border-left-style: solid !important;
          }
          .pdf-container .border-b {
            border-bottom-width: 1px !important;
            border-bottom-style: solid !important;
          }
          .pdf-container .border-b-2 {
            border-bottom-width: 2px !important;
            border-bottom-style: solid !important;
          }
          .pdf-container .border-blue-500 {
            border-color: #3b82f6 !important;
          }
          .pdf-container .border-green-500 {
            border-color: #10b981 !important;
          }
          .pdf-container .border-purple-500 {
            border-color: #8b5cf6 !important;
          }
          .pdf-container .border-orange-500 {
            border-color: #f97316 !important;
          }
          .pdf-container .border-teal-500 {
            border-color: #14b8a6 !important;
          }
          .pdf-container .border-yellow-500 {
            border-color: #eab308 !important;
          }
          .pdf-container .border-indigo-500 {
            border-color: #6366f1 !important;
          }
          .pdf-container .border-blue-200 {
            border-color: #bfdbfe !important;
          }
          .pdf-container .bg-white {
            background-color: white !important;
          }
          .pdf-container .bg-gray-200 {
            background-color: #e5e7eb !important;
          }
          .pdf-container .bg-blue-500 {
            background-color: #3b82f6 !important;
          }
          /* 文字颜色 */
          .pdf-container .text-gray-900 {
            color: #111827 !important;
          }
          .pdf-container .text-gray-800 {
            color: #1f2937 !important;
          }
          .pdf-container .text-gray-700 {
            color: #374151 !important;
          }
          .pdf-container .text-gray-600 {
            color: #4b5563 !important;
          }
          .pdf-container .text-gray-500 {
            color: #6b7280 !important;
          }
          .pdf-container .text-blue-600 {
            color: #2563eb !important;
          }
          /* 其他样式 */
          .pdf-container .font-bold {
            font-weight: 700 !important;
          }
          .pdf-container .font-semibold {
            font-weight: 600 !important;
          }
          .pdf-container .font-medium {
            font-weight: 500 !important;
          }
          .pdf-container .leading-relaxed {
            line-height: 1.625 !important;
          }
          .pdf-container .rounded-full {
            border-radius: 9999px !important;
          }
          .pdf-container .h-1\\.5 {
            height: 6px !important;
          }
          .pdf-container .h-2 {
            height: 8px !important;
          }
          .pdf-container .w-full {
            width: 100% !important;
          }
          .pdf-container .flex {
            display: flex !important;
          }
          .pdf-container .flex-wrap {
            flex-wrap: wrap !important;
          }
          .pdf-container .justify-between {
            justify-content: space-between !important;
          }
          .pdf-container .justify-center {
            justify-content: center !important;
          }
          .pdf-container .items-start {
            align-items: flex-start !important;
          }
          .pdf-container .items-center {
            align-items: center !important;
          }
          .pdf-container .gap-1 {
            gap: 4px !important;
          }
          .pdf-container .pl-2 {
            padding-left: 8px !important;
          }
          .pdf-container .pl-3 {
            padding-left: 12px !important;
          }
          .pdf-container .pb-1 {
            padding-bottom: 4px !important;
          }
          .pdf-container .px-1 {
            padding-left: 4px !important;
            padding-right: 4px !important;
          }
          .pdf-container .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
          /* 防止分页时内容被切断 */
          .page-break-avoid {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
          }
          .page-break-before {
            page-break-before: always !important;
            break-before: always !important;
          }
          .page-break-after {
            page-break-after: always !important;
            break-after: always !important;
          }
          /* 隐藏不需要的元素 */
          .no-print,
          .toolbar,
          .editor-panel,
          header,
          nav,
          .navigation {
            display: none !important;
          }
          /* 确保文字不被遮挡 */
          * {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            box-sizing: border-box !important;
          }
          /* 避免孤立的标题 */
          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid !important;
            break-after: avoid !important;
          }
          /* 避免在表格、图片等元素内分页 */
          table, img, figure {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
          }
        `,
      });

      // 生成PDF
      const pdfBuffer = await page.pdf({
        width: '794px',
        height: '1123px',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        preferCSSPageSize: true,
      });

      // 返回PDF
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="cv-${templateUrl}.pdf"`,
          'Cache-Control': 'no-cache',
        },
      });

    } catch (error) {
      console.error('Error generating PDF:', error);
      console.error('Error stack:', (error as Error).stack);
      return NextResponse.json(
        {
          error: 'Failed to generate PDF',
          details: (error as Error).message,
          stack: process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
        },
        { status: 500 }
      );
    } finally {
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.error('Error closing browser:', closeError);
        }
      }
    }

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: 'PDF generation API is running' });
}
