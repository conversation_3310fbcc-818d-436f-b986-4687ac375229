@import "theme.css";
@import "../styles/pdf-export.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
  .dark {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
}

/* 简历预览区域文字边界处理 */
#cv-preview {
  overflow: visible !important;
  box-sizing: border-box !important;
}

#cv-preview * {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  word-break: break-word !important;
  box-sizing: border-box !important;
}

/* 确保双栏布局中主内容区域有足够空间 */
#cv-preview main {
  min-width: 0 !important;
  flex: 1 !important;
}

/* 长文本内容自动换行 */
#cv-preview .text-content,
#cv-preview .description,
#cv-preview .summary {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  word-break: break-word !important;
  white-space: pre-line !important;
}
