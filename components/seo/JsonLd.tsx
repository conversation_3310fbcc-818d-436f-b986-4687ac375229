interface JsonLdProps {
  data: Record<string, any>;
}

export default function JsonLd({ data }: JsonLdProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

export function WebsiteJsonLd() {
  const data = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "CV Builder Free",
    "description": "Free online CV builder and resume maker with professional templates",
    "url": process.env.NEXT_PUBLIC_WEB_URL || "https://cvbuilder.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${process.env.NEXT_PUBLIC_WEB_URL || "https://cvbuilder.com"}/cv-template?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "CV Builder Free",
      "url": process.env.NEXT_PUBLIC_WEB_URL || "https://cvbuilder.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://image.generatepassword12.org/cvbuilderfreelogo.jpg"
      }
    }
  };

  return <JsonLd data={data} />;
}

export function WebApplicationJsonLd() {
  const data = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "CV Builder Free",
    "description": "Free online CV builder and resume maker with professional templates",
    "url": process.env.NEXT_PUBLIC_WEB_URL || "https://cvbuilder.com",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "softwareVersion": "1.0",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Professional CV Templates",
      "Real-time Preview",
      "PDF Export",
      "ATS-Friendly Designs",
      "Multi-language Support",
      "No Registration Required"
    ],
    "screenshot": "https://image.generatepassword12.org/cvbuilderfreelogo.jpg",
    "creator": {
      "@type": "Organization",
      "name": "CV Builder Free"
    }
  };

  return <JsonLd data={data} />;
}

export function BreadcrumbJsonLd({ items }: { items: Array<{ name: string; url: string }> }) {
  const data = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };

  return <JsonLd data={data} />;
}

export function TemplateJsonLd({ 
  templateName, 
  title, 
  description, 
  industry, 
  style 
}: { 
  templateName: string;
  title: string;
  description: string;
  industry: string;
  style: string;
}) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://cvbuilder.com";
  
  const data = {
    "@context": "https://schema.org",
    "@type": "CreativeWork",
    "name": `${title} CV Template`,
    "description": description,
    "url": `${baseUrl}/cv-template/${templateName}`,
    "creator": {
      "@type": "Organization",
      "name": "CV Builder Free",
      "url": baseUrl
    },
    "isAccessibleForFree": true,
    "genre": [industry, style],
    "keywords": `cv template, resume template, ${title.toLowerCase()}, ${industry.toLowerCase()}, ${style.toLowerCase()}`,
    "audience": {
      "@type": "Audience",
      "audienceType": "Job Seekers"
    },
    "usageInfo": "Free to use for personal and commercial purposes",
    "license": "https://creativecommons.org/licenses/by/4.0/"
  };

  return <JsonLd data={data} />;
}
