import Script from 'next/script'

interface StructuredDataProps {
  type: 'webpage'
  data: {
    name: string
    url: string
    description: string
  }
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: data.name,
    url: data.url,
    description: data.description,
    isPartOf: {
      '@type': 'WebSite',
      name: 'CV Builder Free',
      url: 'https://cvbuilder.com',
    },
  }

  return (
    <Script
      id="structured-data-webpage"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  )
}
