"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Heart } from "lucide-react";
import { useState } from "react";
import type { CVInterest } from "@/types/pages/cv-builder";

export default function Interests() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newInterest, setNewInterest] = useState({
    name: "",
    description: "",
    category: "",
  });

  const interests = data.interests || [];

  const addInterest = () => {
    console.log('addInterest called');
    const interest: CVInterest = {
      id: Date.now().toString(),
      name: "",
      description: undefined,
      category: undefined,
    };
    console.log('Created interest:', interest);
    updateSection("interests", [...interests, interest]);
    setNewInterest({
      name: "",
      description: "",
      category: "",
    });
  };

  const updateInterest = (id: string, updates: Partial<CVInterest>) => {
    const updatedInterests = interests.map((interest) =>
      interest.id === id ? { ...interest, ...updates } : interest
    );
    updateSection("interests", updatedInterests);
  };

  const deleteInterest = (id: string) => {
    const filteredInterests = interests.filter((interest) => interest.id !== id);
    updateSection("interests", filteredInterests);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <Heart size={20} className="text-cyan-500" />
          {t('interests')}
          <span className="text-sm text-gray-500 ml-2">({interests.length} {interests.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addInterest} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {interests.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noInterests')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {interests.map((interest) => (
          <div
            key={interest.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteInterest(interest.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 gap-4">
              <Input
                placeholder={t('interestName')}
                value={interest.name}
                onChange={(e) => updateInterest(interest.id, { name: e.target.value })}
                className="h-10"
              />
            </div>
            <Textarea
              placeholder={t('interestDescription')}
              value={interest.description || ""}
              onChange={(e) => updateInterest(interest.id, { description: e.target.value })}
              rows={3}
              className="resize-none"
            />
          </div>
        ))}
      </div>
    </div>
  );
} 