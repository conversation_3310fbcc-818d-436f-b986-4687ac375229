"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useTranslations } from "next-intl";
import { useEffect } from "react";

function Preview() {
  const { data, sectionsVisible } = useCVBuilder();
  const { 
    personal, 
    profile, 
    jobIntention, 
    work, 
    education, 
    skills,
    projects,
    languages,
    certifications,
    volunteer,
    publications,
    awards,
    interests,
    references
  } = data;
  const t = useTranslations();

  // Debug log to check if data is updating
  useEffect(() => {
    console.log('Preview component data updated:', data);
  }, [data]);

  // Create a unique key for the component to force re-rendering
  const componentKey = `preview-${personal.name}-${projects.length}-${skills.length}-${languages.length}-${certifications.length}-${volunteer.length}-${publications.length}-${awards.length}-${interests.length}-${references.length}`;

  return (
    <Card key={componentKey} id="cv-preview" className={`w-full max-w-xl shadow-lg rounded-xl border border-gray-200 bg-white`}>
      <CardContent className="p-6 space-y-6">
        {/* 个人信息 */}
        {personal && (
          <div className="text-center">
            <Avatar className="w-20 h-20 mx-auto mb-4">
              <AvatarImage src={personal.avatar} alt={personal.name} />
              <AvatarFallback>{personal.name?.charAt(0) || "U"}</AvatarFallback>
            </Avatar>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{personal.name || t("cvbuilder.name")}</h1>
            <p className="text-gray-600 mb-1">{personal.email || t("cvbuilder.email")}</p>
            <p className="text-gray-600 mb-1">{personal.phone || t("cvbuilder.phone")}</p>
            <p className="text-gray-600 mb-1">{personal.address || t("cvbuilder.address")}</p>
            {personal.website && (
              <p className="text-blue-600 text-sm">{personal.website}</p>
            )}
          </div>
        )}

        {/* 求职意向 */}
        {jobIntention?.position && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-2">{t("cvbuilder.jobIntention")}</h2>
            <p className="text-gray-700">{jobIntention.position}</p>
          </div>
        )}

        {/* 个人简介 */}
        {profile?.summary && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-2">{t("cvbuilder.profile")}</h2>
            <p className="text-gray-700 text-sm leading-relaxed">{profile.summary}</p>
          </div>
        )}

        {/* 工作经历 */}
        {work && work.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.work")}</h2>
            <div className="space-y-3">
              {work.map((job, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-3">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900 text-sm">{job.position}</h3>
                    <span className="text-xs text-gray-500">
                      {job.startDateYear && job.startDateMonth ? `${job.startDateYear}年${job.startDateMonth}月` : ""} - 
                      {job.endDateYear && job.endDateMonth ? ` ${job.endDateYear}年${job.endDateMonth}月` : " 至今"}
                    </span>
                  </div>
                  <p className="text-gray-600 text-xs mb-1">{job.company}</p>
                  <p className="text-gray-700 text-xs leading-relaxed">{job.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 教育背景 */}
        {education && education.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.education")}</h2>
            <div className="space-y-3">
              {education.map((edu, index) => (
                <div key={index} className="border-l-4 border-purple-500 pl-3">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900 text-sm">{edu.degree}</h3>
                    <span className="text-xs text-gray-500">
                      {edu.startDateYear && edu.startDateMonth ? `${edu.startDateYear}年${edu.startDateMonth}月` : ""} - 
                      {edu.endDateYear && edu.endDateMonth ? ` ${edu.endDateYear}年${edu.endDateMonth}月` : " 至今"}
                    </span>
                  </div>
                  <p className="text-gray-600 text-xs mb-1">{edu.school}</p>
                  {edu.gpa && <p className="text-gray-600 text-xs">GPA: {edu.gpa}</p>}
                  {edu.description && (
                    <p className="text-gray-700 text-xs mt-1">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 技能 */}
        {skills && skills.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.skills")}</h2>
            <div className="space-y-2">
              {skills.map((skill, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="font-medium text-gray-900 text-sm">{skill.name}</span>
                    <span className="text-xs text-gray-500">{skill.level}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-blue-500 h-1.5 rounded-full"
                      style={{ width: `${skill.level === "精通" ? 90 : skill.level === "熟练" ? 70 : skill.level === "良好" ? 50 : 30}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 项目经验 */}
        {projects && projects.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.projects")}</h2>
            <div className="space-y-3">
              {projects.map((project, index) => (
                <div key={index} className="border-l-4 border-green-500 pl-3">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900 text-sm">{project.name}</h3>
                    <span className="text-xs text-gray-500">
                      {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                    </span>
                  </div>
                  {project.url && (
                    <p className="text-blue-600 text-xs mb-1">{project.url}</p>
                  )}
                  <p className="text-gray-700 text-xs leading-relaxed">{project.description}</p>
                  {project.technologies && project.technologies.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {project.technologies.map((tech, techIndex) => (
                        <span key={techIndex} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 语言能力 */}
        {languages && languages.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.languages")}</h2>
            <div className="space-y-2">
              {languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="font-medium text-gray-900 text-sm">{lang.language}</span>
                  <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded border">
                    {lang.proficiency}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 证书认证 */}
        {certifications && certifications.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.certifications")}</h2>
            <div className="space-y-3">
              {certifications.map((cert, index) => (
                <div key={index} className="border-l-4 border-green-500 pl-3">
                  <h3 className="font-medium text-gray-900 text-sm">{cert.name}</h3>
                  <p className="text-gray-600 text-xs">{cert.issuer}</p>
                  {cert.issueDate && (
                    <p className="text-gray-500 text-xs">{cert.issueDate}</p>
                  )}
                  {cert.url && (
                    <p className="text-blue-600 text-xs">{cert.url}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 志愿服务 */}
        {volunteer && volunteer.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.volunteer")}</h2>
            <div className="space-y-3">
              {volunteer.map((vol, index) => (
                <div key={index} className="border-l-4 border-teal-500 pl-3">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900 text-sm">{vol.role}</h3>
                    <span className="text-xs text-gray-500">
                      {vol.startDate && vol.endDate ? `${vol.startDate} - ${vol.endDate}` : ""}
                    </span>
                  </div>
                  <p className="text-gray-600 text-xs mb-1">{vol.organization}</p>
                  <p className="text-gray-700 text-xs leading-relaxed">{vol.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 发表作品 */}
        {publications && publications.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.publications")}</h2>
            <div className="space-y-3">
              {publications.map((pub, index) => (
                <div key={index} className="border-l-4 border-orange-500 pl-3">
                  <h3 className="font-semibold text-gray-900 text-sm">{pub.title}</h3>
                  <p className="text-gray-600 text-xs">{pub.publication}</p>
                  {pub.date && (
                    <p className="text-gray-500 text-xs">{pub.date}</p>
                  )}
                  {pub.url && (
                    <p className="text-blue-600 text-xs">{pub.url}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 奖项荣誉 */}
        {awards && awards.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.awards")}</h2>
            <div className="space-y-3">
              {awards.map((award, index) => (
                <div key={index} className="border-l-4 border-yellow-500 pl-3">
                  <h3 className="font-medium text-gray-900 text-sm">{award.name}</h3>
                  <p className="text-gray-600 text-xs">{award.issuer}</p>
                  {award.date && (
                    <p className="text-gray-500 text-xs">{award.date}</p>
                  )}
                  {award.description && (
                    <p className="text-gray-700 text-xs mt-1">{award.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 兴趣爱好 */}
        {interests && interests.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.interests")}</h2>
            <div className="space-y-2">
              {interests.map((interest, index) => (
                <div key={index}>
                  <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded mb-1 inline-block">
                    {interest.name}
                  </span>
                  {interest.description && (
                    <p className="text-gray-700 text-xs">{interest.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 推荐人 */}
        {references && references.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">{t("cvbuilder.references")}</h2>
            <div className="space-y-3">
              {references.map((ref, index) => (
                <div key={index} className="border-l-4 border-indigo-500 pl-3">
                  <h3 className="font-medium text-gray-900 text-sm">{ref.name}</h3>
                  <p className="text-gray-600 text-xs">{ref.title}</p>
                  <p className="text-gray-600 text-xs">{ref.company}</p>
                  <p className="text-gray-600 text-xs">{ref.email}</p>
                  {ref.phone && (
                    <p className="text-gray-600 text-xs">{ref.phone}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default Preview; 