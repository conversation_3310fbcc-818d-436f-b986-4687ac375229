"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, Award } from "lucide-react";
import { useState } from "react";
import type { CVCertification } from "@/types/pages/cv-builder";

export default function Certifications() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newCertification, setNewCertification] = useState({
    name: "",
    issuer: "",
    issueDate: "",
    expiryDate: "",
    credentialId: "",
    url: "",
  });

  const certifications = data.certifications || [];

  const addCertification = () => {
    console.log('addCertification called');
    const certification: CVCertification = {
      id: Date.now().toString(),
      name: "",
      issuer: "",
      issueDate: undefined,
      expiryDate: undefined,
      issueDateYear: undefined,
      issueDateMonth: undefined,
      expiryDateYear: undefined,
      expiryDateMonth: undefined,
      credentialId: undefined,
      url: undefined,
    };
    console.log('Created certification:', certification);
    updateSection("certifications", [...certifications, certification]);
    setNewCertification({
      name: "",
      issuer: "",
      issueDate: "",
      expiryDate: "",
      credentialId: "",
      url: "",
    });
  };

  const updateCertification = (id: string, updates: Partial<CVCertification>) => {
    const updatedCertifications = certifications.map((certification) =>
      certification.id === id ? { ...certification, ...updates } : certification
    );
    updateSection("certifications", updatedCertifications);
  };

  const deleteCertification = (id: string) => {
    const filteredCertifications = certifications.filter((certification) => certification.id !== id);
    updateSection("certifications", filteredCertifications);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <Award size={20} className="text-cyan-500" />
          {t('certifications')}
          <span className="text-sm text-gray-500 ml-2">({certifications.length} {certifications.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addCertification} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {certifications.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noCertifications')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {certifications.map((certification) => (
          <div
            key={certification.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteCertification(certification.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('certificationName')}
                value={certification.name}
                onChange={(e) => updateCertification(certification.id, { name: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('issuingOrganization')}
                value={certification.issuer}
                onChange={(e) => updateCertification(certification.id, { issuer: e.target.value })}
                className="h-10"
              />

              {/* Issue Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('issueDate')}</label>
                <div className="flex gap-2">
                  <Select
                    value={certification.issueDateYear || ""}
                    onValueChange={(val) => updateCertification(certification.id, { issueDateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={certification.issueDateMonth || ""}
                    onValueChange={(val) => updateCertification(certification.id, { issueDateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Expiry Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('expiryDate')}</label>
                <div className="flex gap-2">
                  <Select
                    value={certification.expiryDateYear || ""}
                    onValueChange={(val) => updateCertification(certification.id, { expiryDateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() + 10 - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={certification.expiryDateMonth || ""}
                    onValueChange={(val) => updateCertification(certification.id, { expiryDateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Input
                placeholder={t('certificationUrl')}
                value={certification.url || ""}
                onChange={(e) => updateCertification(certification.id, { url: e.target.value })}
                className="h-10"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 