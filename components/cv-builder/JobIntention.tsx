"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";
import { Target } from "lucide-react";

export default function JobIntention() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');

  const handleChange = (field: string, value: string) => {
    updateSection("jobIntention", { ...data.jobIntention, [field]: value });
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <span className="flex items-center gap-2 text-base font-semibold text-blue-600">
          <Target size={20} className="text-cyan-500" />
          {t("jobIntention", { default: "求职意向" })}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="position">{t("expectedPosition", { default: "期望职位" })}</Label>
          <Input
            id="position"
            placeholder={t("expectedPositionPlaceholder", { default: "请输入期望职位" })}
            value={data.jobIntention?.position || ""}
            onChange={(e) => handleChange("position", e.target.value)}
            className="h-10"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="targetSalary">{t("expectedSalary", { default: "期望薪资" })}</Label>
          <Input
            id="targetSalary"
            placeholder={t("expectedSalaryPlaceholder", { default: "请输入期望薪资" })}
            value={data.jobIntention?.targetSalary || ""}
            onChange={(e) => handleChange("targetSalary", e.target.value)}
            className="h-10"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="preferredLocation">{t("preferredLocation", { default: "期望地点" })}</Label>
          <Input
            id="preferredLocation"
            placeholder={t("preferredLocationPlaceholder", { default: "请输入期望工作地点" })}
            value={data.jobIntention?.preferredLocation || ""}
            onChange={(e) => handleChange("preferredLocation", e.target.value)}
            className="h-10"
          />
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="description">{t("description", { default: "补充说明" })}</Label>
          <Textarea
            id="description"
            placeholder={t("jobIntentionPlaceholder", { default: "请输入求职意向的补充说明" })}
            value={data.jobIntention?.description || ""}
            onChange={(e) => handleChange("description", e.target.value)}
            rows={3}
            className="min-h-[80px]"
          />
        </div>
      </div>
    </div>
  );
}