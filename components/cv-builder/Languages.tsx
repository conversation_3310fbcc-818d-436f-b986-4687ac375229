"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Languages as LanguagesIcon } from "lucide-react";
import { useState } from "react";
import type { CVLanguage } from "@/types/pages/cv-builder";

export default function Languages() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newLanguage, setNewLanguage] = useState({
    language: "",
    proficiency: "none",
  });

  const languages = data.languages || [];

  const proficiencyLevels = [
    { value: "native", label: t('native') },
    { value: "fluent", label: t('fluent') },
    { value: "conversational", label: t('conversational') },
    { value: "basic", label: t('basic') },
  ];

  const addLanguage = () => {
    console.log('addLanguage called');
    const language: CVLanguage = {
      id: Date.now().toString(),
      language: "",
      proficiency: "",
    };
    console.log('Created language:', language);
    updateSection("languages", [...languages, language]);
    setNewLanguage({
      language: "",
      proficiency: "none",
    });
  };

  const updateLanguage = (id: string, updates: Partial<CVLanguage>) => {
    const updatedLanguages = languages.map((language) =>
      language.id === id ? { ...language, ...updates } : language
    );
    updateSection("languages", updatedLanguages);
  };

  const deleteLanguage = (id: string) => {
    const filteredLanguages = languages.filter((language) => language.id !== id);
    updateSection("languages", filteredLanguages);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <LanguagesIcon size={20} className="text-cyan-500" />
          {t('languages')}
          <span className="text-sm text-gray-500 ml-2">({languages.length} {languages.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addLanguage} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {languages.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noLanguages')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {languages.map((language) => (
          <div
            key={language.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteLanguage(language.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('languageName')}
                value={language.language}
                onChange={(e) => updateLanguage(language.id, { language: e.target.value })}
                className="h-10"
              />
              <Select
                value={language.proficiency || "none"}
                onValueChange={(value) => updateLanguage(language.id, { proficiency: value })}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder={t('proficiency')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('none')}</SelectItem>
                  {proficiencyLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 