"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, FileText } from "lucide-react";
import { useState } from "react";
import type { CVPublication } from "@/types/pages/cv-builder";

export default function Publications() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newPublication, setNewPublication] = useState({
    title: "",
    authors: "",
    publication: "",
    date: "",
    url: "",
    description: "",
  });

  const publications = data.publications || [];

  const addPublication = () => {
    console.log('addPublication called');
    const publication: CVPublication = {
      id: Date.now().toString(),
      title: "",
      authors: [],
      publication: "",
      date: undefined,
      url: undefined,
      type: undefined,
    };
    console.log('Created publication:', publication);
    updateSection("publications", [...publications, publication]);
    setNewPublication({
      title: "",
      authors: "",
      publication: "",
      date: "",
      url: "",
      description: "",
    });
  };

  const updatePublication = (id: string, updates: Partial<CVPublication>) => {
    const updatedPublications = publications.map((publication) =>
      publication.id === id ? { ...publication, ...updates } : publication
    );
    updateSection("publications", updatedPublications);
  };

  const deletePublication = (id: string) => {
    const filteredPublications = publications.filter((publication) => publication.id !== id);
    updateSection("publications", filteredPublications);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <FileText size={20} className="text-cyan-500" />
          {t('publications')}
          <span className="text-sm text-gray-500 ml-2">({publications.length} {publications.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addPublication} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {publications.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noPublications')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {publications.map((publication) => (
          <div
            key={publication.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deletePublication(publication.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('publicationTitle')}
                value={publication.title}
                onChange={(e) => updatePublication(publication.id, { title: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('publicationAuthors')}
                value={publication.authors.join(", ")}
                onChange={(e) => {
                  const authors = e.target.value.split(",").map(author => author.trim()).filter(author => author);
                  updatePublication(publication.id, { authors });
                }}
                className="h-10"
              />
              <Input
                placeholder="Publication"
                value={publication.publication}
                onChange={(e) => updatePublication(publication.id, { publication: e.target.value })}
                className="h-10"
              />
              {/* Publication Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('publicationDate')}</label>
                <div className="flex gap-2">
                  <Select
                    value={publication.dateYear || ""}
                    onValueChange={(val) => updatePublication(publication.id, { dateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={publication.dateMonth || ""}
                    onValueChange={(val) => updatePublication(publication.id, { dateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Input
                placeholder={t('publicationUrl')}
                value={publication.url || ""}
                onChange={(e) => updatePublication(publication.id, { url: e.target.value })}
                className="h-10"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 