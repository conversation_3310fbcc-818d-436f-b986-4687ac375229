"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, Heart } from "lucide-react";
import { useState } from "react";
import type { CVVolunteer } from "@/types/pages/cv-builder";

export default function Volunteer() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newVolunteer, setNewVolunteer] = useState({
    organization: "",
    role: "",
    startDate: "",
    endDate: "",
    description: "",
    achievements: [],
  });

  const volunteerList = data.volunteer || [];

  const addVolunteer = () => {
    console.log('addVolunteer called');
    const volunteer: CVVolunteer = {
      id: Date.now().toString(),
      organization: "",
      role: "",
      startDate: undefined,
      endDate: undefined,
      description: "",
      achievements: [],
    };
    console.log('Created volunteer:', volunteer);
    updateSection("volunteer", [...volunteerList, volunteer]);
    setNewVolunteer({
      organization: "",
      role: "",
      startDate: "",
      endDate: "",
      description: "",
      achievements: [],
    });
  };

  const updateVolunteer = (id: string, updates: Partial<CVVolunteer>) => {
    const updatedVolunteer = volunteerList.map((item) =>
      item.id === id ? { ...item, ...updates } : item
    );
    updateSection("volunteer", updatedVolunteer);
  };

  const deleteVolunteer = (id: string) => {
    const filteredVolunteer = volunteerList.filter((item) => item.id !== id);
    updateSection("volunteer", filteredVolunteer);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <Heart size={20} className="text-cyan-500" />
          {t('volunteer')}
          <span className="text-sm text-gray-500 ml-2">({volunteerList.length} {volunteerList.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addVolunteer} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {volunteerList.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noVolunteer')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {volunteerList.map((volunteerItem) => (
          <div
            key={volunteerItem.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteVolunteer(volunteerItem.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('volunteerOrganization')}
                value={volunteerItem.organization}
                onChange={(e) => updateVolunteer(volunteerItem.id, { organization: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('volunteerRole')}
                value={volunteerItem.role}
                onChange={(e) => updateVolunteer(volunteerItem.id, { role: e.target.value })}
                className="h-10"
              />
              {/* Start Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('startDate')}</label>
                <div className="flex gap-2">
                  <Select
                    value={volunteerItem.startDateYear || ""}
                    onValueChange={(val) => updateVolunteer(volunteerItem.id, { startDateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={volunteerItem.startDateMonth || ""}
                    onValueChange={(val) => updateVolunteer(volunteerItem.id, { startDateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* End Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('endDate')} ({t('optional')})</label>
                <div className="flex gap-2">
                  <Select
                    value={volunteerItem.endDateYear || ""}
                    onValueChange={(val) => updateVolunteer(volunteerItem.id, { endDateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() + 10 - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={volunteerItem.endDateMonth || ""}
                    onValueChange={(val) => updateVolunteer(volunteerItem.id, { endDateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            <Textarea
              placeholder={t('volunteerDescription')}
              value={volunteerItem.description || ""}
              onChange={(e) => updateVolunteer(volunteerItem.id, { description: e.target.value })}
              rows={3}
              className="resize-none"
            />
          </div>
        ))}
      </div>
    </div>
  );
} 