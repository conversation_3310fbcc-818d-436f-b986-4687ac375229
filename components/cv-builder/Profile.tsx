"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { AlignLeft } from "lucide-react";

function Profile() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations();
  const profile = data.profile;

  function handleChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    updateSection("profile", { summary: e.target.value });
  }

  return (
    <div className="space-y-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="flex items-center gap-2 text-lg font-semibold mb-2 text-blue-600">
        <AlignLeft size={20} className="text-cyan-500" />
        {t("cvbuilder.profile", { default: "Profile / Summary" })}
      </h2>
      <Textarea
        id="profile-summary"
        name="summary"
        value={profile.summary}
        onChange={handleChange}
        rows={5}
        className="min-h-[120px]"
        placeholder={t("cvbuilder.profilePlaceholder", { default: "Write a brief summary about yourself, your career goals, key achievements, and what makes you unique..." })}
      />
    </div>
  );
}

export default Profile; 