"use client";
import React from "react";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { v4 as uuidv4 } from "uuid";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { GraduationCap, Plus } from "lucide-react";

export default function Education() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const education = data.education;

  function handleChange(idx: number, e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { name: string; value: string }) {
    let name: string, value: string;
    if ('target' in e) {
      name = e.target.name;
      value = e.target.value;
    } else {
      name = e.name;
      value = e.value;
    }
    const updated = education.map((item, i) => i === idx ? { ...item, [name]: value } : item);
    updateSection("education", updated);
  }

  function addEducation() {
    const newEducation = { id: uuidv4(), school: "", degree: "", field: "", startDateYear: "", startDateMonth: "", endDateYear: "", endDateMonth: "", description: "", location: "" };
    const updatedEducation = [...education, newEducation];
    console.log('Adding education item:', newEducation);
    console.log('Updated education array:', updatedEducation);
    updateSection("education", updatedEducation);
  }

  function removeEducation(id: string) {
    updateSection("education", education.filter(item => item.id !== id));
  }

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <span className="flex items-center gap-2 text-base font-semibold text-blue-600">
          <GraduationCap size={20} className="text-cyan-500" />
          {t("education", { default: "Education" })}
          <span className="text-sm text-gray-500 ml-2">({education.length} {education.length === 1 ? t('item') : t('items')})</span>
        </span>
        <Button type="button" size="sm" className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform" onClick={addEducation}>
          <Plus size={16} />
          {t("addItem")}
        </Button>
      </div>
      {education.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t("noEducation", { default: "No education yet." })}</div>
          <div className="text-xs mt-1">{t("clickAddToStart", { default: `点击上方'${t('addItem')}'按钮开始` })}</div>
        </div>
      )}
      {education.map((item, idx) => (
        <div key={item.id} className="border border-gray-200 rounded-lg p-4 space-y-2 relative bg-gray-50">
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
            onClick={() => removeEducation(item.id)}
            title={t("remove", { default: "Remove" })}
          >
            ×
          </Button>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Input name="school" value={item.school} onChange={e => handleChange(idx, e)} placeholder={t("school", { default: "School" })} className="h-10" />
            <Input name="degree" value={item.degree} onChange={e => handleChange(idx, e)} placeholder={t("degree", { default: "Degree" })} className="h-10" />
            <Input name="field" value={item.field || ""} onChange={e => handleChange(idx, e)} placeholder={t("field", { default: "Field" })} className="h-10" />
            <div className="flex gap-2">
              <Select value={item.startDateYear || ""} onValueChange={val => handleChange(idx, { name: "startDateYear", value: val })}>
                <SelectTrigger className="w-1/2"><SelectValue placeholder={t("startYear", { default: "Start Year" })} /></SelectTrigger>
                <SelectContent>{[...Array(50)].map((_, i) => { const year = 1975 + i; return <SelectItem key={year} value={String(year)}>{year}</SelectItem>; })}</SelectContent>
              </Select>
              <Select value={item.startDateMonth || ""} onValueChange={val => handleChange(idx, { name: "startDateMonth", value: val })}>
                <SelectTrigger className="w-1/2"><SelectValue placeholder={t("startMonth", { default: "Start Month" })} /></SelectTrigger>
                <SelectContent>{Array.from({ length: 12 }, (_, i) => <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>{i+1}</SelectItem>)}</SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Select value={item.endDateYear || ""} onValueChange={val => handleChange(idx, { name: "endDateYear", value: val })}>
                <SelectTrigger className="w-1/2"><SelectValue placeholder={t("endYear", { default: "End Year" })} /></SelectTrigger>
                <SelectContent>{[...Array(50)].map((_, i) => { const year = 1975 + i; return <SelectItem key={year} value={String(year)}>{year}</SelectItem>; })}</SelectContent>
              </Select>
              <Select value={item.endDateMonth || ""} onValueChange={val => handleChange(idx, { name: "endDateMonth", value: val })}>
                <SelectTrigger className="w-1/2"><SelectValue placeholder={t("endMonth", { default: "End Month" })} /></SelectTrigger>
                <SelectContent>{Array.from({ length: 12 }, (_, i) => <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>{i+1}</SelectItem>)}</SelectContent>
              </Select>
            </div>
            <Textarea name="description" value={item.description || ""} onChange={e => handleChange(idx, e)} placeholder={t("description", { default: "Description" })} rows={4} className="md:col-span-2 min-h-[100px]" />
            <Input name="location" value={item.location || ""} onChange={e => handleChange(idx, e)} placeholder={t("location", { default: "Location" })} className="md:col-span-2 h-10" />
          </div>
        </div>
      ))}
    </div>
  );
}