"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { Input } from "@/components/ui/input";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { v4 as uuidv4 } from "uuid";
import { Sparkles, Plus } from "lucide-react";
import React from "react"; // Added React import for React.ChangeEvent

function Skills() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const skills = data.skills;

  function handleChange(idx: number, e: React.ChangeEvent<HTMLInputElement>) {
    const { name, value } = e.target;
    const updated = skills.map((item, i) => i === idx ? { ...item, [name]: value } : item);
    updateSection("skills", updated);
  }

  function addSkill() {
    updateSection("skills", [
      ...skills,
      { id: uuidv4(), name: "", level: "" }
    ]);
  }

  function removeSkill(id: string) {
    updateSection("skills", skills.filter(item => item.id !== id));
  }

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <span className="flex items-center gap-2 text-base font-semibold text-blue-600">
          <Sparkles size={20} className="text-cyan-500" />
          {t("skills", { default: "Skills" })}
          <span className="text-sm text-gray-500 ml-2">({skills.length} {skills.length === 1 ? t('item') : t('items')})</span>
        </span>
        <Button type="button" size="sm" className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform" onClick={addSkill}>
          <Plus size={16} />
          {t("addItem")}
        </Button>
      </div>
      {skills.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t("noSkills", { default: "No skills yet." })}</div>
          <div className="text-xs mt-1">{t("clickAddToStart", { default: `点击上方'${t('addItem')}'按钮开始` })}</div>
        </div>
      )}
      {skills.map((item, idx) => (
        <div key={item.id} className="border border-gray-200 rounded-lg p-4 space-y-2 relative bg-gray-50">
          <Button
            type="button"
            size="icon"
            variant="ghost"
            className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
            onClick={() => removeSkill(item.id)}
            title={t("remove", { default: "Remove" })}
          >
            ×
          </Button>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Input name="name" value={item.name} onChange={e => handleChange(idx, e)} placeholder={t("skillName", { default: "Skill Name" })} className="h-10" />
            <Input name="level" value={item.level || ""} onChange={e => handleChange(idx, e)} placeholder={t("level", { default: "Level" })} className="h-10" />
          </div>
        </div>
      ))}
    </div>
  );
}

export default Skills;