"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Users } from "lucide-react";
import { useState } from "react";
import type { CVReference } from "@/types/pages/cv-builder";

export default function References() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newReference, setNewReference] = useState({
    name: "",
    title: "",
    company: "",
    email: "",
    phone: "",
    description: "",
  });

  const references = data.references || [];

  const addReference = () => {
    console.log('addReference called');
    const reference: CVReference = {
      id: Date.now().toString(),
      name: "",
      title: "",
      company: "",
      email: undefined,
      phone: undefined,
      relationship: undefined,
    };
    console.log('Created reference:', reference);
    updateSection("references", [...references, reference]);
    setNewReference({
      name: "",
      title: "",
      company: "",
      email: "",
      phone: "",
      description: "",
    });
  };

  const updateReference = (id: string, updates: Partial<CVReference>) => {
    const updatedReferences = references.map((reference) =>
      reference.id === id ? { ...reference, ...updates } : reference
    );
    updateSection("references", updatedReferences);
  };

  const deleteReference = (id: string) => {
    const filteredReferences = references.filter((reference) => reference.id !== id);
    updateSection("references", filteredReferences);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <Users size={20} className="text-cyan-500" />
          {t('references')}
          <span className="text-sm text-gray-500 ml-2">({references.length} {references.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addReference} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {references.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noReferences')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {references.map((reference) => (
          <div
            key={reference.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteReference(reference.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('referenceName')}
                value={reference.name}
                onChange={(e) => updateReference(reference.id, { name: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('referenceTitle')}
                value={reference.title}
                onChange={(e) => updateReference(reference.id, { title: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('referenceCompany')}
                value={reference.company}
                onChange={(e) => updateReference(reference.id, { company: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('referenceEmail')}
                value={reference.email || ""}
                onChange={(e) => updateReference(reference.id, { email: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('referencePhone')}
                value={reference.phone || ""}
                onChange={(e) => updateReference(reference.id, { phone: e.target.value })}
                className="h-10"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 