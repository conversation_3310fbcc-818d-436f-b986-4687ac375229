"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Save, Upload, RefreshCcw, FileText, Eye, EyeOff, Download, UploadCloud, Layout } from "lucide-react";
import { useState, useRef } from "react";
import Link from "next/link";


const SECTION_KEYS = [
  { key: "personal", label: "personalInfo" },
  { key: "profile", label: "profile" },
  { key: "jobIntention", label: "jobIntention" },
  { key: "work", label: "work" },
  { key: "education", label: "education" },
  { key: "skills", label: "skills" },
  { key: "projects", label: "projects" },
  { key: "languages", label: "languages" },
  { key: "certifications", label: "certifications" },
  { key: "awards", label: "awards" },
  { key: "interests", label: "interests" },
  { key: "volunteer", label: "volunteer" },
  { key: "publications", label: "publications" },
  { key: "references", label: "references" },
];

function Toolbar() {
  const { save, load, reset, data, sectionsVisible, toggleSectionVisible, setData } = useCVBuilder();
  const t = useTranslations();
  const [showSection, setShowSection] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 本地存储功能
  function handleSave() {
    save();
    toast.success(t("saved", { default: "Saved to local storage!" }));
  }

  function handleLoad() {
    load();
    toast.success(t("loaded", { default: "Loaded from local storage!" }));
  }

  function handleReset() {
    reset();
    toast.success(t("reset", { default: "Reset to default!" }));
  }

  // PDF 导出功能 - 使用 Puppeteer 生成高质量PDF
  async function handleExportPDF() {
    let loadingToast: any = null;
    try {
      // 获取当前模板名称
      const currentPath = window.location.pathname;
      const templateMatch = currentPath.match(/\/cv-template\/([^\/]+)/);
      const templateName = templateMatch ? templateMatch[1] : 'modern-it-two-column';
      const templateUrl = `/cv-template/${templateName}/pdf`;

      const userName = data.personal?.name || "Resume";
      const fileName = `CV-${userName}.pdf`;

      console.log('PDF generation request:', { templateUrl, templateName, dataSize: JSON.stringify(data).length });

      loadingToast = toast.loading(t("cvbuilder.generatingPDF", { default: "Generating PDF... This may take 10-20 seconds." }));

      // 调用新的 Puppeteer PDF 生成 API
      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateUrl,
          data
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.dismiss(loadingToast);
      toast.success(t("cvbuilder.pdfGenerated", { default: "PDF generated successfully!" }));
    } catch (error) {
      console.error('PDF generation failed:', error);
      if (loadingToast) {
        toast.dismiss(loadingToast);
      }
      toast.error(t("cvbuilder.pdfGenerationFailed", { default: "PDF generation failed!" }));
    }
  }

  // JSON 文件导出功能 - 使用用户姓名作为文件名
  function handleExportJSON() {
    const userName = data.personal?.name || "Resume";
    const fileName = `CV-${userName}.json`;

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
    toast.success(t("cvbuilder.exportedJSON", { default: "Exported as JSON!" }));
  }

  // JSON 文件导入功能
  function handleImportJSON(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.name.match(/\.(json)$/)) {
      toast.error(t("cvbuilder.invalidFile", { default: "Please select a valid JSON file!" }));
      return;
    }

    const reader = new FileReader();
    reader.onload = (evt) => {
      try {
        const json = JSON.parse(evt.target?.result as string);
        setData(json);
        toast.success(t("cvbuilder.importedJSON", { default: "Successfully imported from JSON!" }));
      } catch {
        toast.error(t("cvbuilder.invalidJSON", { default: "Invalid JSON file format!" }));
      }
    };
    reader.readAsText(file);

    // 清空文件输入，允许重复选择同一文件
    e.target.value = '';
  }

  return (
    <div className="sticky top-0 z-30 mb-6 bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-300/80 shadow-none rounded-xl flex justify-center items-center gap-4 px-6 py-3 backdrop-blur border border-blue-100/40">
      {/* 模板中心按钮 */}
      <Link href="/cv-template">
        <Button
          variant="outline"
          size="sm"
          className="bg-white/80 hover:bg-white flex items-center gap-2"
          title="浏览模板"
        >
          <Layout size={16} />
          模板中心
        </Button>
      </Link>

      {/* 分隔线 */}
      <div className="w-px h-6 bg-white/30"></div>

      {/* 样式控制区 */}
      <div className="flex items-center gap-2">




        {/* 区块显示/隐藏 */}
        <div className="relative">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowSection((v) => !v)}
            className="bg-white/80 hover:bg-white"
            title={t("cvbuilder.toggleSections", { default: "Toggle Sections" })}
          >
            <Eye size={18} />
          </Button>
          {showSection && (
            <div className="absolute left-0 top-12 bg-white border rounded-lg shadow-lg p-2 flex flex-col gap-1 z-50 min-w-[160px]">
              {SECTION_KEYS.map((s) => (
                <button
                  key={s.key}
                  className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-50 transition-colors"
                  onClick={() => toggleSectionVisible(s.key)}
                >
                  {sectionsVisible[s.key] ?
                    <Eye size={16} className="text-green-500" /> :
                    <EyeOff size={16} className="text-gray-400" />
                  }
                  <span className={sectionsVisible[s.key] ? 'text-gray-900' : 'text-gray-500'}>
                    {t(`cvbuilder.${s.label}`)}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 分隔线 */}
      <div className="w-px h-6 bg-white/30"></div>

      {/* 文件操作区 */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportJSON}
          className="bg-white/80 hover:bg-white flex items-center gap-2"
          title={t("cvbuilder.exportJSON", { default: "Export as JSON" })}
        >
          <Download size={16} />
          <span className="hidden sm:inline">JSON</span>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          className="bg-white/80 hover:bg-white flex items-center gap-2"
          title={t("cvbuilder.importJSON", { default: "Import from JSON" })}
        >
          <UploadCloud size={16} />
          <span className="hidden sm:inline">{t("cvbuilder.import", { default: "Import" })}</span>
        </Button>

        <input
          type="file"
          accept="application/json"
          ref={fileInputRef}
          onChange={handleImportJSON}
          className="hidden"
        />
      </div>

      {/* 分隔线 */}
      <div className="w-px h-6 bg-white/30"></div>

      {/* 主要操作区 */}
      <div className="flex items-center gap-2">
        <Button
          onClick={handleSave}
          size="sm"
          className="bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-cyan-500 hover:to-blue-600 text-white flex items-center gap-2 shadow-sm"
        >
          <Save size={16} />
          {t("save", { default: "Save" })}
        </Button>

        <Button
          onClick={handleLoad}
          variant="secondary"
          size="sm"
          className="bg-white/80 text-blue-700 hover:bg-white flex items-center gap-2"
        >
          <Upload size={16} />
          {t("load", { default: "Load" })}
        </Button>

        <Button
          onClick={handleReset}
          variant="destructive"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCcw size={16} />
          {t("reset", { default: "Reset" })}
        </Button>

        <Button
          onClick={handleExportPDF}
          variant="outline"
          size="sm"
          className="bg-white/80 hover:bg-white flex items-center gap-2 font-medium"
        >
          <FileText size={16} />
          {t("exportPDF", { default: "Export PDF" })}
        </Button>
      </div>
    </div>
  );
}

export default Toolbar; 