"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useTranslations } from "next-intl";
import { useRef } from "react";
import { User, Camera, X } from "lucide-react";

function PersonalInfo() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations();
  const personal = data.personal;
  const fileInputRef = useRef<HTMLInputElement>(null);

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { name, value } = e.target;
    updateSection("personal", { ...personal, [name]: value });
  }

  function handleAvatarUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (file) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        alert(t("cvbuilder.invalidImageFile", { default: "Please select a valid image file!" }));
        return;
      }

      // 验证文件大小 (最大 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert(t("cvbuilder.imageTooLarge", { default: "Image file is too large! Please select a file smaller than 5MB." }));
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        updateSection("personal", { ...personal, avatar: result });
      };
      reader.readAsDataURL(file);
    }
    // 清空文件输入，允许重复选择同一文件
    e.target.value = '';
  }

  function removeAvatar() {
    updateSection("personal", { ...personal, avatar: "" });
  }

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="flex items-center gap-2 text-lg font-semibold mb-2 text-blue-600">
        <User size={20} className="text-cyan-500" />
        {t("cvbuilder.personalInfo", { default: "Personal Information" })}
      </h2>

      {/* 头像上传区域 */}
      <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
        <Avatar className="w-20 h-20">
          <AvatarImage src={personal.avatar || undefined} alt={personal.name || "Avatar"} />
          <AvatarFallback className="text-lg font-semibold bg-gradient-to-r from-blue-400 to-cyan-400 text-white">
            {personal.name?.[0]?.toUpperCase() || "?"}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center gap-2"
          >
            <Camera size={16} />
            {t("cvbuilder.uploadAvatar", { default: "Upload Avatar" })}
          </Button>
          {personal.avatar && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={removeAvatar}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <X size={16} />
              {t("cvbuilder.removeAvatar", { default: "Remove" })}
            </Button>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleAvatarUpload}
            className="hidden"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input id="name" name="name" value={personal.name} onChange={handleChange} required placeholder={t("cvbuilder.name", { default: "Name" })} className="h-10" />
        <Input id="email" name="email" value={personal.email} onChange={handleChange} required placeholder={t("cvbuilder.email", { default: "Email" })} className="h-10" />
        <Input id="phone" name="phone" value={personal.phone || ""} onChange={handleChange} placeholder={t("cvbuilder.phone", { default: "Phone" })} className="h-10" />
        <Input id="address" name="address" value={personal.address || ""} onChange={handleChange} placeholder={t("cvbuilder.address", { default: "Address" })} className="h-10" />
        <Input id="website" name="website" value={personal.website || ""} onChange={handleChange} placeholder={t("cvbuilder.website", { default: "Website" })} className="h-10" />
        <Input id="linkedin" name="linkedin" value={personal.linkedin || ""} onChange={handleChange} placeholder={t("cvbuilder.linkedin", { default: "LinkedIn" })} className="h-10" />
        <Input id="github" name="github" value={personal.github || ""} onChange={handleChange} placeholder={t("cvbuilder.github", { default: "GitHub" })} className="md:col-span-2 h-10" />
      </div>
    </div>
  );
}

export default PersonalInfo; 