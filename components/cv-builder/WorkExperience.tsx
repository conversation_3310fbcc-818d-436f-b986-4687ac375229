"use client";
import { use<PERSON><PERSON><PERSON>er } from "@/contexts/cv-builder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { v4 as uuidv4 } from "uuid";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, Plus, Briefcase } from "lucide-react";
import { useState, useEffect } from "react";
import React from "react"; // Explicitly import React for React.ChangeEvent types
import type { ChangeEvent } from "react";
import type { CVData } from "@/types/pages/cv-builder";

// 新增：单独的可排序工作项组件
interface SortableWorkItemProps {
  item: CVData["work"][number];
  idx: number;
  handleChange: (idx: number, e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { name: string; value: string }) => void;
  removeWork: (id: string) => void;
  currentYear: number;
  t: (key: string, options?: Record<string, any>) => string;
}

function SortableWorkItem({ item, idx, handleChange, removeWork, currentYear, t }: SortableWorkItemProps) {
  const { setNodeRef, listeners, attributes, transform, transition, isDragging } = useSortable({ id: item.id });
  return (
    <div
      key={item.id}
      ref={setNodeRef}
      className={`border border-gray-200 rounded-lg p-4 space-y-2 relative bg-gray-50 transition-shadow ${isDragging ? 'ring-2 ring-blue-300 shadow-md' : ''}`}
      style={{ transform: CSS.Transform.toString(transform), transition }}
    >
      <Button
        type="button"
        size="icon"
        variant="ghost"
        className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
        onClick={() => removeWork(item.id)}
        title={t("remove", { default: "Remove" })}
      >
        ×
      </Button>
      <div
        className={`absolute left-2 top-2 z-10 h-8 w-8 flex items-center justify-center cursor-grab rounded text-blue-500 bg-white/80 hover:text-cyan-500 active:cursor-grabbing ${isDragging ? 'text-cyan-500 bg-cyan-50' : ''}`}
        style={{ boxShadow: isDragging ? '0 0 0 2px #22d3ee' : undefined }}
        {...attributes}
        {...listeners}
        tabIndex={0}
        aria-label="Drag handle"
      >
        <GripVertical size={22} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 pl-8">
        <Input name="company" value={item.company} onChange={e => handleChange(idx, e)} placeholder={t("company", { default: "Company" })} className="h-10" />
        <Input name="position" value={item.position} onChange={e => handleChange(idx, e)} placeholder={t("position", { default: "Position" })} className="h-10" />
        <div className="flex gap-2">
          <Select value={item.startDateYear || ""} onValueChange={val => handleChange(idx, { name: "startDateYear", value: val })}>
            <SelectTrigger className="w-1/2"><SelectValue placeholder={t("startYear", { default: "Start Year" })} /></SelectTrigger>
            <SelectContent>{[...Array(currentYear-1975+1)].map((_, i) => { const year = 1975 + i; return <SelectItem key={year} value={String(year)}>{year}</SelectItem>; })}</SelectContent>
          </Select>
          <Select value={item.startDateMonth || ""} onValueChange={val => handleChange(idx, { name: "startDateMonth", value: val })}>
            <SelectTrigger className="w-1/2"><SelectValue placeholder={t("startMonth", { default: "Start Month" })} /></SelectTrigger>
            <SelectContent>{Array.from({ length: 12 }, (_, i) => <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>{i+1}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div className="flex gap-2">
          <Select value={item.endDateYear || ""} onValueChange={val => handleChange(idx, { name: "endDateYear", value: val })}>
            <SelectTrigger className="w-1/2"><SelectValue placeholder={t("endYear", { default: "End Year" })} /></SelectTrigger>
            <SelectContent>{[...Array(currentYear-1975+1)].map((_, i) => { const year = 1975 + i; return <SelectItem key={year} value={String(year)}>{year}</SelectItem>; })}</SelectContent>
          </Select>
          <Select value={item.endDateMonth || ""} onValueChange={val => handleChange(idx, { name: "endDateMonth", value: val })}>
            <SelectTrigger className="w-1/2"><SelectValue placeholder={t("endMonth", { default: "End Month" })} /></SelectTrigger>
            <SelectContent>{Array.from({ length: 12 }, (_, i) => <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>{i+1}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <Textarea name="description" value={item.description || ""} onChange={e => handleChange(idx, e)} placeholder={t("description", { default: "Description" })} rows={4} className="md:col-span-2 min-h-[100px]" />
        <Input name="location" value={item.location || ""} onChange={e => handleChange(idx, e)} placeholder={t("location", { default: "Location" })} className="md:col-span-2 h-10" />
      </div>
    </div>
  );
}

function WorkExperience() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const work = data.work;
  const sensors = useSensors(useSensor(PointerSensor));
  const [currentYear, setCurrentYear] = useState(2024);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
    setMounted(true);
  }, []);

  function handleChange(idx: number, e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | { name: string; value: string }) {
    let name: string, value: string;
    if ('target' in e) {
      name = e.target.name;
      value = e.target.value;
    } else {
      name = e.name;
      value = e.value;
    }
    const updated = work.map((item, i) => i === idx ? { ...item, [name]: value } : item);
    updateSection("work", updated);
  }

  function addWork() {
    const newWork = { id: uuidv4(), company: "", position: "", startDateYear: "", startDateMonth: "", endDateYear: "", endDateMonth: "", description: "", location: "" };
    const updatedWork = [...work, newWork];
    console.log('Adding work item:', newWork);
    console.log('Updated work array:', updatedWork);
    updateSection("work", updatedWork);
  }

  function removeWork(id: string) {
    updateSection("work", work.filter(item => item.id !== id));
  }

  function handleDragEnd(event: any) {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = work.findIndex((item) => item.id === active.id);
      const newIndex = work.findIndex((item) => item.id === over.id);
      updateSection("work", arrayMove(work, oldIndex, newIndex));
    }
  }

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <span className="flex items-center gap-2 text-base font-semibold text-blue-600">
          <Briefcase size={20} className="text-cyan-500" />
          {t("workExperience", { default: "Work Experience" })}
          <span className="text-sm text-gray-500 ml-2">({work.length} {work.length === 1 ? t('item', { default: 'item' }) : t('items', { default: 'items' })})</span>
        </span>
        <Button type="button" size="sm" className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform" onClick={addWork}>
          <Plus size={16} />
          {t("addItem")}
        </Button>
      </div>
      {work.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t("noWork", { default: "暂无工作经历" })}</div>
          <div className="text-xs mt-1">{t("clickAddToStart", { default: `点击上方'${t('addItem')}'按钮开始` })}</div>
        </div>
      )}
      {mounted && (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={work.map(item => item.id)} strategy={verticalListSortingStrategy}>
            {work.map((item, idx) => (
              <SortableWorkItem
                key={item.id}
                item={item}
                idx={idx}
                handleChange={handleChange}
                removeWork={removeWork}
                currentYear={currentYear}
                t={t}
              />
            ))}
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
}

export default WorkExperience;