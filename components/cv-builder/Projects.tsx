"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, X, FolderOpen } from "lucide-react";
import { useState } from "react";
import type { CVProject } from "@/types/pages/cv-builder";

export default function Projects() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newProject, setNewProject] = useState<Partial<CVProject>>({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    technologies: [],
    role: "",
    teamSize: "",
    achievements: [],
    url: "",
    image: "",
  });

  const projects = data.projects || [];

  const addProject = () => {
    console.log('addProject called');
    const project: CVProject = {
      id: Date.now().toString(),
      name: "",
      description: "",
      startDate: undefined,
      endDate: undefined,
      technologies: [],
      role: undefined,
      teamSize: undefined,
      achievements: [],
      url: undefined,
      image: undefined,
    };
    console.log('Created project:', project);
    updateSection("projects", [...projects, project]);
    setNewProject({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      technologies: [],
      role: "",
      teamSize: "",
      achievements: [],
      url: "",
      image: "",
    });
  };

  const updateProject = (id: string, updates: Partial<CVProject>) => {
    const updatedProjects = projects.map((project) =>
      project.id === id ? { ...project, ...updates } : project
    );
    updateSection("projects", updatedProjects);
  };

  const deleteProject = (id: string) => {
    const filteredProjects = projects.filter((project) => project.id !== id);
    updateSection("projects", filteredProjects);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <FolderOpen size={20} className="text-cyan-500" />
          {t('projects')}
          <span className="text-sm text-gray-500 ml-2">({projects.length} {projects.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addProject} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {projects.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noProjects')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {projects.map((project) => (
          <div
            key={project.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteProject(project.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('projectName')}
                value={project.name}
                onChange={(e) => updateProject(project.id, { name: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('projectUrl')}
                value={project.url || ""}
                onChange={(e) => updateProject(project.id, { url: e.target.value })}
                className="h-10"
              />
            </div>

            <Textarea
              placeholder={t('projectDescription')}
              value={project.description || ""}
              onChange={(e) => updateProject(project.id, { description: e.target.value })}
              rows={3}
              className="resize-none"
            />

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">{t('projectTechnologies')} ({t('optional')})</label>
              <Input
                placeholder={t('projectTechnologiesPlaceholder')}
                value={project.technologies?.join(", ") || ""}
                onChange={(e) => {
                  const technologies = e.target.value.split(",").map(tech => tech.trim()).filter(tech => tech);
                  updateProject(project.id, { technologies });
                }}
                className="h-10"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">{t('projectAchievements')} ({t('optional')})</label>
              <Textarea
                placeholder={t('projectAchievementsPlaceholder')}
                value={project.achievements?.join("\n") || ""}
                onChange={(e) => {
                  const achievements = e.target.value.split("\n").filter(achievement => achievement.trim());
                  updateProject(project.id, { achievements });
                }}
                rows={3}
                className="resize-none"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 