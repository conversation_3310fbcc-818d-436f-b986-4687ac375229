"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import PersonalInfo from "@/components/cv-builder/PersonalInfo";
import Profile from "@/components/cv-builder/Profile";
import JobIntention from "@/components/cv-builder/JobIntention";
import WorkExperience from "@/components/cv-builder/WorkExperience";
import Education from "@/components/cv-builder/Education";
import Skills from "@/components/cv-builder/Skills";
import Projects from "@/components/cv-builder/Projects";
import Languages from "@/components/cv-builder/Languages";
import Certifications from "@/components/cv-builder/Certifications";
import Awards from "@/components/cv-builder/Awards";
import Interests from "@/components/cv-builder/Interests";
import Volunteer from "@/components/cv-builder/Volunteer";
import Publications from "@/components/cv-builder/Publications";
import References from "@/components/cv-builder/References";
import Preview from "@/components/cv-builder/Preview";
import Toolbar from "@/components/cv-builder/Toolbar";
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, ArrowLeft } from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

// 可拖拽的模块组件
function DraggableSection({ id, children }: { id: string; children: React.ReactNode }) {
  const { setNodeRef, listeners, attributes, transform, transition, isDragging } = useSortable({ id });

  return (
    <div
      ref={setNodeRef}
      className={`relative ${isDragging ? 'z-50' : ''}`}
      style={{ transform: CSS.Transform.toString(transform), transition }}
    >
      <div
        className={`absolute -left-2 top-2 z-10 h-8 w-8 flex items-center justify-center cursor-grab rounded text-gray-400 hover:text-blue-500 hover:bg-blue-50 ${isDragging ? 'text-blue-500 bg-blue-50' : ''}`}
        {...attributes}
        {...listeners}
        tabIndex={0}
        aria-label="Drag to reorder section"
      >
        <GripVertical size={16} />
      </div>
      <div className={`pl-6 ${isDragging ? 'opacity-50' : ''}`}>
        {children}
      </div>
    </div>
  );
}

export default function EditorLayout({
  previewComponent,
  isFullScreen = false,
  hideToolbar = false
}: {
  previewComponent?: React.ReactNode;
  isFullScreen?: boolean;
  hideToolbar?: boolean;
} = {}) {
  const { data, sectionsVisible, sectionOrder, setSectionOrder } = useCVBuilder();
  const t = useTranslations();
  const sensors = useSensors(useSensor(PointerSensor));
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 使用useMemo确保预览组件响应数据变化
  const memoizedPreviewComponent = useMemo(() => {
    return previewComponent;
  }, [previewComponent, data]); // 添加data作为依赖项

  // 组件映射 - 包含所有编辑组件
  const sectionComponents = {
    personal: PersonalInfo,
    profile: Profile,
    jobIntention: JobIntention,
    work: WorkExperience,
    education: Education,
    skills: Skills,
    projects: Projects,
    languages: Languages,
    certifications: Certifications,
    awards: Awards,
    interests: Interests,
    volunteer: Volunteer,
    publications: Publications,
    references: References,
  };

  function handleDragEnd(event: any) {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = sectionOrder.indexOf(active.id);
      const newIndex = sectionOrder.indexOf(over.id);
      setSectionOrder(arrayMove(sectionOrder, oldIndex, newIndex));
    }
  }

  // 全屏布局
  if (isFullScreen) {
    return (
      <div className={`${hideToolbar ? 'h-[calc(100vh-4rem)]' : 'h-screen'} bg-gradient-to-br from-white via-blue-50 to-blue-100`}>
        <div className="h-full flex flex-col">
          {!hideToolbar && <Toolbar />}
          <div className="flex-1 flex gap-4 p-4 overflow-hidden">
            {/* 编辑区 */}
            <section className="w-1/2 space-y-4 flex-shrink-0">
              <div className="h-full overflow-y-auto pr-2 bg-white/90 rounded-lg p-4 shadow-lg">
                {mounted ? (
                  <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                    <SortableContext items={sectionOrder} strategy={verticalListSortingStrategy}>
                      {sectionOrder.map((sectionId) => {
                        const Component = sectionComponents[sectionId as keyof typeof sectionComponents];
                        if (!Component || !sectionsVisible[sectionId]) return null;

                        return (
                          <DraggableSection key={sectionId} id={sectionId}>
                            <Component />
                          </DraggableSection>
                        );
                      })}
                    </SortableContext>
                  </DndContext>
                ) : (
                  <div className="space-y-4">
                    {sectionOrder.map((sectionId) => {
                      const Component = sectionComponents[sectionId as keyof typeof sectionComponents];
                      if (!Component || !sectionsVisible[sectionId]) return null;

                      return (
                        <div key={sectionId} className="pl-6">
                          <Component />
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </section>

            {/* 预览区 - 使用Preview组件 */}
            <section className="w-1/2 flex items-start justify-center bg-gray-50 rounded-lg p-4 shadow-lg">
              <div 
                className="w-full h-full flex items-start justify-center overflow-auto"
              >
                <div 
                  className="bg-white shadow-lg rounded-lg"
                  style={{
                    width: '100%',
                    maxWidth: '800px',
                    minHeight: 'auto',
                    height: 'auto',
                    overflow: 'visible'
                  }}
                >
                  <div id="cv-preview" className="w-full h-full">
                    {memoizedPreviewComponent ? (
                      <div key={`preview-${data.personal?.name || ''}-${data.work?.length || 0}-${data.education?.length || 0}-${data.skills?.length || 0}-${data.projects?.length || 0}-${data.languages?.length || 0}-${data.certifications?.length || 0}-${data.volunteer?.length || 0}-${data.publications?.length || 0}-${data.awards?.length || 0}-${data.interests?.length || 0}-${data.references?.length || 0}`}>
                        {memoizedPreviewComponent}
                      </div>
                    ) : (
                      <Preview />
                    )}
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    );
  }

  // 默认布局
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 py-2">
      <div className="w-full mx-auto flex flex-col gap-2 px-2">
        {!hideToolbar && <Toolbar />}
        <div className="flex flex-col lg:flex-row gap-2 shadow-lg rounded-xl bg-white/90 p-2">
          {/* 编辑区 - 与预览区同样大 */}
          <section className="w-full lg:w-1/2 space-y-4 flex-shrink-0">
            <div className="max-h-[calc(100vh-100px)] overflow-y-auto pr-2">
              {mounted ? (
                <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                  <SortableContext items={sectionOrder} strategy={verticalListSortingStrategy}>
                    {sectionOrder.map((sectionId) => {
                      const Component = sectionComponents[sectionId as keyof typeof sectionComponents];
                      if (!Component || !sectionsVisible[sectionId]) return null;

                      return (
                        <DraggableSection key={sectionId} id={sectionId}>
                          <Component />
                        </DraggableSection>
                      );
                    })}
                  </SortableContext>
                </DndContext>
              ) : (
                <div className="space-y-4">
                  {sectionOrder.map((sectionId) => {
                    const Component = sectionComponents[sectionId as keyof typeof sectionComponents];
                    if (!Component || !sectionsVisible[sectionId]) return null;

                    return (
                      <div key={sectionId} className="pl-6">
                        <Component />
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </section>

          {/* 预览区 - 使用Preview组件 */}
          <section className="w-full lg:w-1/2 flex items-start justify-center bg-gray-50 rounded-lg p-4 shadow-lg">
            <div 
              className="w-full h-full flex items-start justify-center overflow-auto"
            >
              <div 
                className="bg-white shadow-lg rounded-lg"
                style={{
                  width: '100%',
                  maxWidth: '800px',
                  minHeight: 'auto',
                  height: 'auto',
                  overflow: 'visible'
                }}
              >
                <div id="cv-preview" className="w-full h-full">
                  {memoizedPreviewComponent ? (
                    <div key={`preview-${data.personal?.name || ''}-${data.work?.length || 0}-${data.education?.length || 0}-${data.skills?.length || 0}-${data.projects?.length || 0}-${data.languages?.length || 0}-${data.certifications?.length || 0}-${data.volunteer?.length || 0}-${data.publications?.length || 0}-${data.awards?.length || 0}-${data.interests?.length || 0}-${data.references?.length || 0}`}>
                      {memoizedPreviewComponent}
                    </div>
                  ) : (
                    <Preview />
                  )}
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}