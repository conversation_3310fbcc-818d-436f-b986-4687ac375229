"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Award } from "lucide-react";
import { useState } from "react";
import type { CVAward } from "@/types/pages/cv-builder";

export default function Awards() {
  const { data, updateSection } = useCVBuilder();
  const t = useTranslations('cvbuilder');
  const [newAward, setNewAward] = useState({
    name: "",
    issuer: "",
    date: "",
    description: "",
    category: "none",
  });

  const awards = data.awards || [];

  const awardCategories = [
    { value: "academic", label: t('academic') },
    { value: "professional", label: t('professional') },
    { value: "leadership", label: t('leadership') },
    { value: "innovation", label: t('innovation') },
    { value: "service", label: t('service') },
    { value: "competition", label: t('competition') },
    { value: "other", label: t('other') },
  ];

  const addAward = () => {
    console.log('addAward called');
    const award: CVAward = {
      id: Date.now().toString(),
      name: "",
      issuer: "",
      date: undefined,
      description: undefined,
      category: undefined,
    };
    console.log('Created award:', award);
    updateSection("awards", [...awards, award]);
    setNewAward({
      name: "",
      issuer: "",
      date: "",
      description: "",
      category: "none",
    });
  };

  const updateAward = (id: string, updates: Partial<CVAward>) => {
    const updatedAwards = awards.map((award) =>
      award.id === id ? { ...award, ...updates } : award
    );
    updateSection("awards", updatedAwards);
  };

  const deleteAward = (id: string) => {
    const filteredAwards = awards.filter((award) => award.id !== id);
    updateSection("awards", filteredAwards);
  };

  return (
    <div className="space-y-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <h2 className="flex items-center gap-2 text-lg font-semibold text-blue-600">
          <Award size={20} className="text-cyan-500" />
          {t('awards')}
          <span className="text-sm text-gray-500 ml-2">({awards.length} {awards.length === 1 ? t('item') : t('items')})</span>
        </h2>
        <Button 
          onClick={addAward} 
          size="sm" 
          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full px-4 py-1 font-bold hover:scale-105 transition-transform"
        >
          <Plus size={16} />
          {t('addItem')}
        </Button>
      </div>

      {awards.length === 0 && (
        <div className="text-center py-8 text-gray-400">
          <div className="text-sm">{t('noAwards')}</div>
          <div className="text-xs mt-1">{t('clickAddToStart')}</div>
        </div>
      )}

      <div className="space-y-4">
        {awards.map((award) => (
          <div
            key={award.id}
            className="border border-gray-200 rounded-lg p-4 space-y-4 relative bg-gray-50"
          >
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50"
              onClick={() => deleteAward(award.id)}
              title={t('delete')}
            >
              <X size={16} />
            </Button>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder={t('awardName')}
                value={award.name}
                onChange={(e) => updateAward(award.id, { name: e.target.value })}
                className="h-10"
              />
              <Input
                placeholder={t('awardIssuer')}
                value={award.issuer}
                onChange={(e) => updateAward(award.id, { issuer: e.target.value })}
                className="h-10"
              />
              {/* Award Date */}
              <div className="space-y-2">
                <label className="text-sm text-gray-600">{t('awardDate')}</label>
                <div className="flex gap-2">
                  <Select
                    value={award.dateYear || ""}
                    onValueChange={(val) => updateAward(award.id, { dateYear: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('year')} />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(50)].map((_, i) => {
                        const year = new Date().getFullYear() - i;
                        return <SelectItem key={year} value={String(year)}>{year}</SelectItem>;
                      })}
                    </SelectContent>
                  </Select>
                  <Select
                    value={award.dateMonth || ""}
                    onValueChange={(val) => updateAward(award.id, { dateMonth: val })}
                  >
                    <SelectTrigger className="w-1/2">
                      <SelectValue placeholder={t('month')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                          {String(i+1).padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Select
                value={award.category || "none"}
                onValueChange={(value) => updateAward(award.id, { category: value })}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder={t('awardCategory')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('none')}</SelectItem>
                  {awardCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Textarea
              placeholder={t('awardDescription')}
              value={award.description || ""}
              onChange={(e) => updateAward(award.id, { description: e.target.value })}
              rows={3}
              className="resize-none"
            />
          </div>
        ))}
      </div>
    </div>
  );
} 