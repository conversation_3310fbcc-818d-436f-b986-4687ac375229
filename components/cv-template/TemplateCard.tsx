import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { TemplateMeta } from "./templates/meta";
import { FileText, Briefcase, Palette, Sparkles, Building, GraduationCap } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

// 根据模板风格和行业选择合适的图标和渐变色
const getTemplateVisuals = (template: TemplateMeta) => {
  const styleGradients = {
    "现代": "from-blue-500 to-purple-600",
    "经典": "from-gray-600 to-gray-800",
    "极简": "from-slate-400 to-slate-600",
    "创意": "from-pink-500 to-orange-500",
    "优雅": "from-purple-600 to-indigo-700",
    "科技": "from-cyan-500 to-blue-600"
  };

  const industryIcons = {
    "IT/开发": FileText,
    "IT": FileText,
    "创业": Sparkles,
    "产品经理": Briefcase,
    "金融": Building,
    "法律": Building,
    "传统企业": Building,
    "学术": GraduationCap,
    "海外求职": FileText,
    "通用": FileText,
    "UI/UX设计师": Palette,
    "艺术/媒体": Palette,
    "市场营销": Sparkles,
    "高端服务": Sparkles,
    "时尚/奢侈品": Sparkles,
    "咨询/顾问": Briefcase,
    "科技创业": Sparkles,
    "设计师": Palette,
    "高管": Briefcase
  };

  const gradient = styleGradients[template.style as keyof typeof styleGradients] || "from-gray-500 to-gray-700";
  const IconComponent = industryIcons[template.industry as keyof typeof industryIcons] || FileText;

  return { gradient, IconComponent };
};

// 为每个模板生成吸引人的标语
const getTemplateTagline = (template: TemplateMeta, locale: string, t: any) => {
  return t(`cvTemplate.templateTaglines.${template.name}`) || (locale === 'en' ? template.descriptionEn : template.description) || template.description;
};

export default function TemplateCard({ template }: { template: TemplateMeta }) {
  const t = useTranslations();
  const locale = useLocale();
  const { gradient, IconComponent } = getTemplateVisuals(template);
  const tagline = getTemplateTagline(template, locale, t);

  // 根据语言选择显示的文本
  const getLocalizedText = (zhText: string, enText?: string) => {
    return locale === 'en' && enText ? enText : zhText;
  };

  return (
    <Link href={`/cv-template/${template.name}`}
      className="group block bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-gray-200 transform hover:-translate-y-1">

      {/* 头部渐变区域 */}
      <div className={`relative h-32 bg-gradient-to-br ${gradient} flex items-center justify-center`}>
        <div className="text-center text-white">
          <IconComponent size={32} className="mx-auto mb-2 opacity-90" />
          <h3 className="font-bold text-lg">{getLocalizedText(template.title, template.titleEn)}</h3>
        </div>

        {/* 右上角样式标签 */}
        <div className="absolute top-3 right-3">
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs">
            {getLocalizedText(template.style, template.styleEn)}
          </Badge>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-5">
        {/* 标语 */}
        <p className="text-gray-700 font-medium mb-3 text-sm leading-relaxed">
          {tagline}
        </p>

        {/* 标签区域 */}
        <div className="flex flex-wrap gap-2 mb-3">
          <Badge variant="outline" className="text-xs">
            {getLocalizedText(template.layout, template.layoutEn)}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {getLocalizedText(template.industry, template.industryEn)}
          </Badge>
        </div>

        {/* 底部描述 */}
        <div className="text-xs text-gray-500 line-clamp-2">
          {getLocalizedText(template.description, template.descriptionEn)}
        </div>

        {/* 悬停效果指示 */}
        <div className="mt-3 text-xs text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          {t('cvTemplate.clickToPreview')}
        </div>
      </div>
    </Link>
  );
}