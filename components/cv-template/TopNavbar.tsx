"use client";
import { useC<PERSON>uilder } from "@/contexts/cv-builder";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import {
  Save,
  Upload,
  RefreshCcw,
  FileText,
  Eye,
  EyeOff,
  Download,
  UploadCloud,
  Layout,
  ArrowLeft,
  Sun,
  Moon,
  Globe
} from "lucide-react";
import { useState, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { useTheme } from "next-themes";
import { useRouter, usePathname } from "next/navigation";



const SECTION_KEYS = [
  { key: "personal", label: "personalInfo" },
  { key: "profile", label: "profile" },
  { key: "jobIntention", label: "jobIntention" },
  { key: "work", label: "work" },
  { key: "education", label: "education" },
  { key: "skills", label: "skills" },
  { key: "projects", label: "projects" },
  { key: "languages", label: "languages" },
  { key: "certifications", label: "certifications" },
  { key: "awards", label: "awards" },
  { key: "interests", label: "interests" },
  { key: "volunteer", label: "volunteer" },
  { key: "publications", label: "publications" },
  { key: "references", label: "references" },
];

interface TopNavbarProps {
  templateName?: string;
  templateTitle?: string;
}

export default function TopNavbar({ templateName, templateTitle }: TopNavbarProps) {
  const {
    save,
    load,
    reset,
    data,
    sectionsVisible,
    toggleSectionVisible,
    setData
  } = useCVBuilder();
  
  const t = useTranslations();
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  
  const [showSection, setShowSection] = useState(false);
  const [showLanguage, setShowLanguage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 本地存储功能
  function handleSave() {
    save();
    toast.success(t("saved", { default: "Saved to local storage!" }), { duration: 1500 });
  }

  function handleLoad() {
    load();
    toast.success(t("loaded", { default: "Loaded from local storage!" }), { duration: 1500 });
  }

  function handleReset() {
    reset();
    toast.success(t("reset", { default: "Reset to default!" }), { duration: 1500 });
  }

  // PDF 导出功能
  async function handleExportPDF() {
    let loadingToast: any = null;
    try {
      const templateUrl = `/cv-template/${templateName}/pdf`;
      const userName = data.personal?.name || "Resume";
      const fileName = `CV-${userName}.pdf`;

      console.log('PDF generation request:', { templateUrl, templateName, dataSize: JSON.stringify(data).length });
      
      loadingToast = toast.loading(t("cvbuilder.generatingPDF", { default: "Generating PDF... This may take 10-20 seconds." }));

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateUrl,
          data
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.dismiss(loadingToast);
      toast.success(t("cvbuilder.pdfGenerated", { default: "PDF generated successfully!" }), { duration: 2000 });
    } catch (error) {
      console.error('PDF generation failed:', error);
      if (loadingToast) {
        toast.dismiss(loadingToast);
      }
      toast.error(t("cvbuilder.pdfGenerationFailed", { default: "PDF generation failed!" }), { duration: 3000 });
    }
  }

  // JSON 文件导出功能
  function handleExportJSON() {
    const userName = data.personal?.name || "Resume";
    const fileName = `CV-${userName}.json`;

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success(t("cvbuilder.jsonExported", { default: "JSON exported successfully!" }), { duration: 1500 });
  }

  // JSON 文件导入功能
  function handleImportJSON() {
    fileInputRef.current?.click();
  }

  function handleFileChange(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0];
    if (file && file.type === "application/json") {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string);
          setData(jsonData);
          toast.success(t("cvbuilder.jsonImported", { default: "JSON imported successfully!" }), { duration: 1500 });
        } catch (error) {
          toast.error(t("cvbuilder.invalidJson", { default: "Invalid JSON file!" }), { duration: 2000 });
        }
      };
      reader.readAsText(file);
    } else {
      toast.error(t("cvbuilder.selectJsonFile", { default: "Please select a JSON file!" }), { duration: 2000 });
    }
    event.target.value = "";
  }

  // 语言切换
  function handleLanguageChange(value: string) {
    const pathSegments = pathname.split('/').filter(Boolean); // 移除空字符串
    const currentLocale = pathSegments[0]; // 第一个段可能是语言代码

    // 检查第一个段是否是有效的语言代码
    const isCurrentLocaleValid = ['en', 'zh'].includes(currentLocale);

    if (isCurrentLocaleValid) {
      // 如果当前路径有语言前缀，替换它
      if (value !== currentLocale) {
        const newPathName = `/${value}/${pathSegments.slice(1).join('/')}`;
        router.push(newPathName);
      }
    } else {
      // 如果当前路径没有语言前缀，添加语言前缀
      const newPathName = `/${value}${pathname}`;
      router.push(newPathName);
    }
    setShowLanguage(false);
  }

  // 主题切换
  function toggleTheme() {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  }

  return (
    <nav className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 shadow-sm sticky top-0 z-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* 左侧：Logo + 网站名称 + 返回按钮 */}
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <Image src="https://image.generatepassword12.org/cvbuilderfreelogo.jpg" alt="CV Builder Free" width={32} height={32} className="rounded" />
              <span className="text-xl font-bold text-gray-900 dark:text-white">CV Builder Free</span>
            </Link>

            <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
            
            <Link href="/cv-template">
              <Button variant="ghost" size="sm" className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Templates</span>
              </Button>
            </Link>

            {templateTitle && (
              <>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">{templateTitle}</span>
                </div>
              </>
            )}
          </div>

          {/* 中间：工具栏功能 */}
          <div className="flex items-center space-x-2">
            {/* 保存/加载 */}
            <div className="flex items-center space-x-1 bg-gray-50 dark:bg-gray-800 rounded-lg p-1">
              <Button variant="ghost" size="sm" onClick={handleSave} className="h-8 px-2">
                <Save size={16} />
                <span className="hidden sm:inline ml-1">Save</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={handleLoad} className="h-8 px-2">
                <Upload size={16} />
                <span className="hidden sm:inline ml-1">Load</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={handleReset} className="h-8 px-2">
                <RefreshCcw size={16} />
                <span className="hidden sm:inline ml-1">Reset</span>
              </Button>
            </div>

            {/* JSON导入导出功能 */}
            <div className="flex items-center space-x-1 bg-gray-50 dark:bg-gray-800 rounded-lg p-1">
              <Button variant="ghost" size="sm" onClick={handleExportJSON} className="h-8 px-2">
                <Download size={16} />
                <span className="hidden sm:inline ml-1">JSON</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={handleImportJSON} className="h-8 px-2">
                <UploadCloud size={16} />
                <span className="hidden sm:inline ml-1">Import</span>
              </Button>
            </div>

            {/* 样式设置 */}
            <div className="flex items-center space-x-1 bg-gray-50 dark:bg-gray-800 rounded-lg p-1">




              {/* 模块显示/隐藏 */}
              <div className="relative">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowSection(!showSection)}
                  className="h-8 px-2"
                >
                  <Layout size={16} />
                  <span className="hidden sm:inline ml-1">Sections</span>
                </Button>
                {showSection && (
                  <div className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-2 z-50">
                    <div className="space-y-1 w-48">
                      {SECTION_KEYS.map((section) => (
                        <button
                          key={section.key}
                          onClick={() => toggleSectionVisible(section.key)}
                          className="w-full p-2 text-left text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between text-gray-900 dark:text-gray-100"
                        >
                          <span>{t(`cvbuilder.${section.label}`, { default: section.label })}</span>
                          {sectionsVisible[section.key] ? (
                            <Eye size={14} className="text-green-500" />
                          ) : (
                            <EyeOff size={14} className="text-gray-400" />
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* PDF导出按钮 - 强化样式 */}
          <div className="flex items-center">
            <Button
              onClick={() => {
                window.open(`/cv-template/${templateName}/pdf?print=1`, '_blank');
              }}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 border-0"
              size="sm"
            >
              <FileText size={18} className="mr-2" />
              <span className="text-sm">Export / Print</span>
            </Button>
          </div>

          {/* 右侧：语言切换 + 主题切换 */}
          <div className="flex items-center space-x-2">
            {/* 语言切换 */}
            <div className="relative">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowLanguage(!showLanguage)}
                className="h-8 px-2"
              >
                <Globe size={16} />
                <span className="hidden sm:inline ml-1">
                  {(() => {
                    const pathSegments = pathname.split('/').filter(Boolean);
                    const currentLocale = pathSegments[0];
                    return ['en', 'zh'].includes(currentLocale)
                      ? (currentLocale === 'zh' ? '中文' : 'EN')
                      : 'EN'; // 默认显示英文
                  })()}
                </span>
              </Button>
              {showLanguage && (
                <div className="absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-1 z-50">
                  <button
                    onClick={() => handleLanguageChange('en')}
                    className="block w-full px-3 py-2 text-sm text-left rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    English
                  </button>
                  <button
                    onClick={() => handleLanguageChange('zh')}
                    className="block w-full px-3 py-2 text-sm text-left rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    中文
                  </button>
                </div>
              )}
            </div>

            {/* 主题切换 */}
            <Button variant="ghost" size="sm" onClick={toggleTheme} className="h-8 px-2">
              {theme === 'dark' ? <Sun size={16} /> : <Moon size={16} />}
            </Button>
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".json"
        className="hidden"
      />

      {/* 点击外部关闭下拉菜单 */}
      {(showSection || showLanguage) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowSection(false);
            setShowLanguage(false);
          }}
        />
      )}
    </nav>
  );
}
