import { Dispatch, SetStateAction } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { X, Filter } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { TEMPLATES } from "./templates/meta";

// 从模板数据中动态获取所有可用的选项
const getUniqueValues = (field: 'style' | 'layout' | 'industry', locale: string) => {
  const values = new Set<string>();
  TEMPLATES.forEach(template => {
    const value = locale === 'en' && template[`${field}En` as keyof typeof template]
      ? template[`${field}En` as keyof typeof template] as string
      : template[field];
    if (value) values.add(value);
  });
  return ["", ...Array.from(values).sort()];
};

export default function FilterBar({ filters, setFilters }: {
  filters: { style: string; layout: string; industry: string };
  setFilters: Dispatch<SetStateAction<{ style: string; layout: string; industry: string }>>;
}) {
  const t = useTranslations();
  const locale = useLocale();
  const hasActiveFilters = filters.style || filters.layout || filters.industry;

  // 获取当前语言的选项
  const styles = getUniqueValues('style', locale);
  const layouts = getUniqueValues('layout', locale);
  const industries = getUniqueValues('industry', locale);

  const clearAllFilters = () => {
    setFilters({ style: "", layout: "", industry: "" });
  };

  const clearFilter = (filterType: 'style' | 'layout' | 'industry') => {
    setFilters(f => ({ ...f, [filterType]: "" }));
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center gap-3 mb-4">
        <Filter size={20} className="text-gray-600" />
        <h3 className="font-semibold text-gray-800">{t('cvTemplate.filterTitle')}</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-500 hover:text-gray-700 ml-auto"
          >
            <X size={16} className="mr-1" />
            {t('cvTemplate.clearAll')}
          </Button>
        )}
      </div>

      {/* 筛选器选择 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('cvTemplate.designStyle')}</label>
          <select
            className="w-full border border-gray-200 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            value={filters.style}
            onChange={e => setFilters(f => ({ ...f, style: e.target.value }))}
          >
            {styles.map(s => (
              <option key={s} value={s}>{s ? s : t('cvTemplate.allStyles')}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('cvTemplate.layoutType')}</label>
          <select
            className="w-full border border-gray-200 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            value={filters.layout}
            onChange={e => setFilters(f => ({ ...f, layout: e.target.value }))}
          >
            {layouts.map(l => (
              <option key={l} value={l}>{l ? l : t('cvTemplate.allLayouts')}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">{t('cvTemplate.targetIndustry')}</label>
          <select
            className="w-full border border-gray-200 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            value={filters.industry}
            onChange={e => setFilters(f => ({ ...f, industry: e.target.value }))}
          >
            {industries.map(i => (
              <option key={i} value={i}>{i ? i : t('cvTemplate.allIndustries')}</option>
            ))}
          </select>
        </div>
      </div>

      {/* 活跃筛选器标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600">{t('cvTemplate.selectedFilters')}:</span>
          {filters.style && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.style}
              <button onClick={() => clearFilter('style')} className="ml-1 hover:bg-gray-300 rounded-full p-0.5">
                <X size={12} />
              </button>
            </Badge>
          )}
          {filters.layout && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.layout}
              <button onClick={() => clearFilter('layout')} className="ml-1 hover:bg-gray-300 rounded-full p-0.5">
                <X size={12} />
              </button>
            </Badge>
          )}
          {filters.industry && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.industry}
              <button onClick={() => clearFilter('industry')} className="ml-1 hover:bg-gray-300 rounded-full p-0.5">
                <X size={12} />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}