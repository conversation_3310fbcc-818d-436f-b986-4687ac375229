"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ClassicCorporateTimelineProps {
  data?: any;
  isExport?: boolean;
}

export default function ClassicCorporateTimeline({
  data: propData,
  isExport = false
}: ClassicCorporateTimelineProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1a1a1a',
    boxShadow: isExport ? 'none' : '0 4px 24px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.6',
    fontFamily: 'Times New Roman, serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="text-center mb-8 border-b-2 border-gray-800 pb-6">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-4"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-4 flex items-center justify-center bg-gray-800 text-white font-bold"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-bold text-gray-900 mb-3 ${isExport ? 'text-2xl' : 'text-3xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <div className={`text-gray-700 font-semibold mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {data.jobIntention.position}
          </div>
        )}
        <div className="flex justify-center flex-wrap gap-4 text-gray-600">
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && (
            <span className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
          )}
          {data.personal?.linkedin && (
            <span className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
          )}
          {data.personal?.github && (
            <span className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
          )}
        </div>
      </header>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`pdf-item bg-gray-50 p-4 rounded border-l-4 border-gray-600`}>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.targetSalary", { default: "Target Salary" })}:</span> {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "Preferred Location" })}:</span> {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.personalProfile", { default: "个人简介" })}
          </h2>
          <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item`}>
            {data.profile.summary}
          </div>
        </section>
      )}

      {/* Work Experience Timeline */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.workExperience", { default: "工作经历" })}
          </h2>
          <div className="space-y-6">
            {(data.work || []).map((job: any, index: number) => (
              <div key={job.id || index} className="relative pl-8 border-l-2 border-gray-300 pdf-item">
                <div className="absolute left-[-8px] top-2 w-4 h-4 bg-gray-800 rounded-full"></div>
                <div className="mb-2">
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{job.position}</h3>
                  <p className={`text-gray-700 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{job.company}</p>
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                  </p>
                  {job.location && (
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.location}</p>
                  )}
                </div>
                {job.description && (
                  <p className={`text-gray-700 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.description}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Education Timeline */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          <div className="space-y-6">
            {(data.education || []).map((edu: any, index: number) => (
              <div key={edu.id || index} className="relative pl-8 border-l-2 border-gray-300 pdf-item">
                <div className="absolute left-[-8px] top-2 w-4 h-4 bg-gray-800 rounded-full"></div>
                <div className="mb-2">
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                  <p className={`text-gray-700 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                  </p>
                  {edu.location && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</p>}
                  {edu.gpa && <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</p>}
                </div>
                {edu.description && (
                  <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.description}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.skills", { default: "专业技能" })}
          </h2>
          <div className="grid grid-cols-2 gap-4">
            {(data.skills || []).map((skill: any, index: number) => (
              <div key={skill.id || index} className="pdf-item">
                <div className={`font-semibold text-gray-900 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</div>
                {skill.level && (
                  <Badge className={`bg-gray-200 text-gray-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                    {skill.level}
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Projects */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="space-y-6">
            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className="relative pl-8 border-l-2 border-gray-300 pdf-item">
                <div className="absolute left-[-8px] top-2 w-4 h-4 bg-gray-800 rounded-full"></div>
                <div className="mb-2">
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                  {project.url && (
                    <p className={`text-gray-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                  )}
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                  </p>
                </div>
                <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                {project.technologies && project.technologies.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {project.technologies.map((tech: string, techIndex: number) => (
                      <Badge key={techIndex} className={`bg-gray-200 text-gray-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {tech}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className="grid grid-cols-2 gap-4">
            {(data.languages || []).map((lang: any, index: number) => (
              <div key={index} className="pdf-item">
                <div className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className="space-y-4">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="pdf-item flex justify-between items-start">
                <div>
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                </div>
                {award.date && (
                  <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</span>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.certifications", { default: "专业认证" })}
          </h2>
          <div className="space-y-4">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                {cert.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-3">
            {(data.interests || []).map((interest: any, index: number) => (
              <span key={interest.id || index} className={`px-3 py-1 bg-gray-100 text-gray-700 rounded ${isExport ? 'text-sm' : 'text-base'}`}>
                {interest.name || interest}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-6">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="pdf-item relative pl-8">
                <div className="absolute left-0 top-2 w-3 h-3 bg-gray-400 rounded-full"></div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</p>
                  </div>
                  <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                  </span>
                </div>
                {vol.description && (
                  <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {vol.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-4">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                {pub.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section">
          <h2 className={`font-bold text-gray-900 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                {ref.phone && (
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}