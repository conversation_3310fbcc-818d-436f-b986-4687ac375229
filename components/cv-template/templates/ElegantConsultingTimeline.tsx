"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ElegantConsultingTimelineProps {
  data?: any;
  isExport?: boolean;
}

export default function ElegantConsultingTimeline({
  data: propData,
  isExport = false
}: ElegantConsultingTimelineProps) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  const containerStyle = isExport ? {
    fontSize: '12px',
    lineHeight: '1.4',
    padding: '40px',
    maxWidth: '210mm',
    minHeight: '297mm',
    fontFamily: 'PingFang SC, Microsoft YaHei, SimSun, sans-serif'
  } : {};

  return (
    <div className="pdf-container bg-white text-gray-800 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="pdf-section pdf-item text-center py-8 border-b border-gray-200 mb-8">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className={`mx-auto rounded-full object-cover mb-4 ${isExport ? 'w-20 h-20' : 'w-24 h-24'}`}
          />
        )}
        <h1 className={`font-serif font-bold text-gray-900 tracking-widest mb-2 ${isExport ? 'text-3xl' : 'text-4xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <p className={`text-indigo-400 mb-3 ${isExport ? 'text-lg' : 'text-xl'}`}>{data.jobIntention.position}</p>
        )}
        <div className={`flex justify-center flex-wrap gap-6 text-gray-400 ${isExport ? 'text-sm' : 'text-base'}`}>
          <span>{data.personal?.email || "<EMAIL>"}</span>
          <span>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && <span>{data.personal.website}</span>}
          {data.personal?.linkedin && <span>LinkedIn: {data.personal.linkedin}</span>}
          {data.personal?.github && <span>GitHub: {data.personal.github}</span>}
        </div>
      </header>
      {/* Profile */}
      {data.profile.summary && (
        <div className="pdf-section pdf-item mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-2 border-l-4 border-indigo-400 pl-3">{t("cvbuilder.profile", { default: "Profile" })}</h2>
          <p className="text-gray-700 leading-relaxed text-lg">{data.profile.summary}</p>
        </div>
      )}
      {/* Work Experience Timeline */}
      <div className="pdf-section pdf-item mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3">{t("cvbuilder.work", { default: "Work Experience" })}</h2>
        <div className="space-y-6">
          {data.work.map((job: any, index: number) => (
            <div key={index} className="relative pl-8 border-l-2 border-indigo-200">
              <div className="absolute left-[-8px] top-2 w-4 h-4 bg-indigo-400 rounded-full"></div>
              <div className="mb-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-bold text-gray-800">{job.position}</h3>
                  <span className="text-sm text-gray-400">{formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}</span>
                </div>
                <p className="text-gray-600 font-medium">{job.company}</p>
              </div>
              {job.description && <p className="text-gray-700 mt-1">{job.description}</p>}
            </div>
          ))}
        </div>
      </div>
      {/* Education Timeline */}
      <div className="pdf-section pdf-item mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3">{t("cvbuilder.education", { default: "Education" })}</h2>
        <div className="space-y-6">
          {data.education.map((edu: any, index: number) => (
            <div key={index} className="relative pl-8 border-l-2 border-indigo-200">
              <div className="absolute left-[-8px] top-2 w-4 h-4 bg-indigo-400 rounded-full"></div>
              <div className="mb-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-bold text-gray-800">{edu.degree}</h3>
                  <span className="text-sm text-gray-400">{formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}</span>
                </div>
                <p className="text-gray-600 font-medium">{edu.school}</p>
              </div>
              {edu.description && <p className="text-gray-700 mt-1">{edu.description}</p>}
            </div>
          ))}
        </div>
      </div>
      {/* Skills */}
      <div className="pdf-section pdf-item">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3">{t("cvbuilder.skills", { default: "Skills" })}</h2>
        <div className="flex flex-wrap gap-3">
          {(data.skills || []).map((skill: any, index: number) => (
            <span key={skill.id || index} className={`px-4 py-2 bg-indigo-50 text-indigo-800 rounded-lg font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
              {skill.name}
            </span>
          ))}
        </div>
      </div>

      {/* Projects */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="space-y-6">
            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg pdf-item">
                <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                {project.url && <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</div>}
                {project.startDate && project.endDate && (
                  <div className={`text-gray-500 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {project.startDate} - {project.endDate}
                  </div>
                )}
                {project.description && (
                  <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{project.description}</div>
                )}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {project.technologies.map((tech: string, techIndex: number) => (
                      <span key={techIndex} className={`px-2 py-1 bg-indigo-100 text-indigo-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {tech}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-3'}`}>
            {(data.languages || []).map((lang: any, index: number) => (
              <div key={index} className="text-center p-3 bg-indigo-50 rounded-lg pdf-item">
                <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                <div className={`text-indigo-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className="space-y-4">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</div>
                {cert.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className="space-y-4">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                {award.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-3">
            {(data.interests || []).map((interest: any, index: number) => (
              <span key={interest.id || index} className={`px-3 py-1 bg-indigo-100 text-indigo-700 rounded ${isExport ? 'text-sm' : 'text-base'}`}>
                {interest.name || interest}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-4">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="pdf-item">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                  </div>
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                  </div>
                </div>
                {vol.description && (
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                    {vol.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-4">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                {pub.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-indigo-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className={`grid gap-6 ${isExport ? 'grid-cols-1' : 'grid-cols-2'}`}>
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                {ref.phone && (
                  <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}