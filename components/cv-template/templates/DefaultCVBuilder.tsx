"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface DefaultCVBuilderProps {
  data?: any;
  isExport?: boolean;
}

export default function DefaultCVBuilder({
  data: propData,
  isExport = false
}: DefaultCVBuilderProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 默认CV Builder风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '30px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1f2937',
    boxShadow: isExport ? 'none' : '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      <div className={`grid gap-8 ${isExport ? 'grid-cols-2' : 'grid-cols-1 lg:grid-cols-3'}`}>
        <aside className={`${isExport ? '' : 'lg:col-span-1'} space-y-6`}>
          {/* 头像渲染 */}
          {data.personal?.avatar && (
            <img
              src={data.personal.avatar}
              alt={data.personal.name || 'Avatar'}
              className="pdf-avatar mx-auto mb-4"
              style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
            />
          )}
          {!data.personal?.avatar && (
            <div
              className="pdf-avatar mx-auto mb-4 flex items-center justify-center bg-blue-100 text-blue-600 font-bold"
              style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
            >
              {data.personal?.name?.[0]?.toUpperCase() || '?'}
            </div>
          )}

          <div className={`font-bold mb-3 text-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
          </div>
          {data.jobIntention?.position && (
            <div className={`text-blue-600 mb-4 text-center font-medium ${isExport ? 'text-base' : 'text-lg'}`}>
              {data.jobIntention.position}
            </div>
          )}

          {/* 联系信息 */}
          <div className="space-y-2 mb-6">
            <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
              📧 {data.personal?.email || "<EMAIL>"}
            </div>
            <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
              📱 {data.personal?.phone || "+86 138 0000 0000"}
            </div>
            <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
              📍 {data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}
            </div>
            {data.personal?.website && (
              <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                🌐 {data.personal.website}
              </div>
            )}
            {data.personal?.linkedin && (
              <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                💼 LinkedIn: {data.personal.linkedin}
              </div>
            )}
            {data.personal?.github && (
              <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                💻 GitHub: {data.personal.github}
              </div>
            )}
          </div>

          {/* 个人简介 */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.personalProfile", { default: "个人简介" })}
              </h2>
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item`}>
                {data.profile.summary}
              </div>
            </section>
          )}

          {/* 技能 */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.skills", { default: "专业技能" })}
              </h2>
              <div className="space-y-2">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="pdf-item">
                    <div className={`text-gray-800 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
                      • {skill.name}
                      {skill.level && (
                        <Badge className={`ml-2 bg-blue-100 text-blue-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                          {skill.level}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 语言 */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-2">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="pdf-item flex justify-between items-center">
                    <span className={`text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                    <span className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 兴趣爱好 */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-2 py-1 bg-blue-100 text-blue-800 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </section>
          )}

          {/* 证书 */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</div>
                    <div className={`text-blue-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</div>
                    {(cert.issueDateYear || cert.issueDate) && (
                      <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {cert.issueDateYear && cert.issueDateMonth
                          ? formatDate(cert.issueDateYear, cert.issueDateMonth, locale)
                          : cert.issueDate || cert.date
                        }
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </aside>

        <main className={`${isExport ? '' : 'lg:col-span-2'} space-y-8`}>
          {/* 求职意向 */}
          {sectionsVisible.jobIntention !== false && data.jobIntention?.position && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.jobIntention", { default: "求职意向" })}
              </h2>
              <div className="pdf-item">
                <div className={`font-semibold mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{data.jobIntention.position}</div>
                {data.jobIntention.targetSalary && (
                  <div className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>💰 {t("cvbuilder.expectedSalary", { default: "期望薪资" })}: {data.jobIntention.targetSalary}</div>
                )}
                {data.jobIntention.preferredLocation && (
                  <div className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>📍 {t("cvbuilder.preferredLocation", { default: "期望地点" })}: {data.jobIntention.preferredLocation}</div>
                )}
                {data.jobIntention.description && (
                  <div className={`text-gray-700 mt-3 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{data.jobIntention.description}</div>
                )}
              </div>
            </section>
          )}

          {/* 工作经历 */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-6">
                {(data.work || []).map((work: any, index: number) => (
                  <div key={work.id || index} className="pdf-item border-l-4 border-blue-200 pl-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                        <div className={`text-blue-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                        {work.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{work.location}</div>}
                      </div>
                      <div className={`text-gray-500 text-right ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                      </div>
                    </div>
                    {work.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {work.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 项目经历 */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-6">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="pdf-item border-l-4 border-green-200 pl-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                        {project.url && <div className={`text-green-600 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</div>}
                      </div>
                      {project.startDate && project.endDate && (
                        <div className={`text-gray-500 text-right ${isExport ? 'text-sm' : 'text-base'}`}>
                          {project.startDate} - {project.endDate}
                        </div>
                      )}
                    </div>
                    {project.description && (
                      <div className={`text-gray-700 leading-relaxed mb-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {project.description}
                      </div>
                    )}
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} className={`bg-green-100 text-green-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 教育经历 */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-6">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={edu.id || index} className="pdf-item border-l-4 border-purple-200 pl-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                        <div className={`text-purple-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
                          {edu.degree} {edu.field && `- ${edu.field}`}
                        </div>
                        {edu.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</div>}
                        {edu.gpa && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</div>}
                      </div>
                      <div className={`text-gray-500 text-right ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                      </div>
                    </div>
                    {edu.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {edu.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 获奖经历 */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-4">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="pdf-item border-l-4 border-yellow-200 pl-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{award.name}</h3>
                        <div className={`text-yellow-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                      </div>
                      {award.date && (
                        <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 志愿者经历 */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-6">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="pdf-item border-l-4 border-red-200 pl-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{vol.organization}</h3>
                        <div className={`text-red-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                      </div>
                      <div className={`text-gray-500 text-right ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </div>
                    </div>
                    {vol.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {vol.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 出版物 */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-4">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="pdf-item border-l-4 border-indigo-200 pl-4">
                    <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{pub.title}</h3>
                    <div className={`text-indigo-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                    {pub.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* 推荐人 */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold border-b-2 border-blue-500 pb-2 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className={`grid gap-6 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="pdf-item border-l-4 border-pink-200 pl-4">
                    <h3 className={`font-bold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{ref.name}</h3>
                    <div className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>📧 {ref.email}</div>
                    {ref.phone && (
                      <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>📱 {ref.phone}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </main>
      </div>
    </div>
  );
}