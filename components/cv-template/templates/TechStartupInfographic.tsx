"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface TechStartupInfographicProps {
  data?: any;
  isExport?: boolean;
}

export default function TechStartupInfographic({
  data: propData,
  isExport = false
}: TechStartupInfographicProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 科技创业信息图风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '15px' : '30px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#2d3748',
    boxShadow: isExport ? 'none' : '0 8px 32px rgba(0,0,0,0.12)',
    borderRadius: isExport ? '0' : '12px',
    fontSize: isExport ? '11px' : '13px',
    lineHeight: isExport ? '1.3' : '1.4',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-800 mx-auto" style={containerStyle}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-blue-500 rounded-full"></div>
        <div className="absolute top-40 right-20 w-24 h-24 border-2 border-purple-500 rounded-full"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 border-2 border-green-500 rounded-full"></div>
      </div>

      <div className="relative z-10">
        {/* Header */}
        <header className="text-center mb-10">
          {data.personal?.avatar && (
            <img
              src={data.personal.avatar}
              alt={data.personal.name || 'Avatar'}
              className="pdf-avatar mx-auto mb-6"
              style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
            />
          )}
          {!data.personal?.avatar && (
            <div
              className="pdf-avatar mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100 text-blue-600 font-bold"
              style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
            >
              {data.personal?.name?.[0]?.toUpperCase() || '?'}
            </div>
          )}
          <h1 className={`font-bold text-gray-900 mb-3 tracking-wide ${isExport ? 'text-2xl' : 'text-3xl'}`}>
            {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
          </h1>
          {data.jobIntention?.position && (
            <div className={`text-gray-700 mb-4 font-medium ${isExport ? 'text-lg' : 'text-xl'}`}>
              {data.jobIntention.position}
            </div>
          )}
          <div className="flex justify-center space-x-6 text-gray-600">
            <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
            <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
            <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          </div>
          {(data.personal?.website || data.personal?.linkedin || data.personal?.github) && (
            <div className="flex justify-center space-x-6 mt-3 text-gray-600">
              {data.personal?.website && (
                <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
              )}
              {data.personal?.linkedin && (
                <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
              )}
              {data.personal?.github && (
                <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
              )}
            </div>
          )}
        </header>

        {/* Job Intention */}
        {sectionsVisible.jobIntention !== false && data.jobIntention && (
          <section className="mb-10 pdf-section">
            <h2 className={`font-bold text-center mb-6 text-green-600 ${isExport ? 'text-xl' : 'text-2xl'}`}>
              {t("cvbuilder.jobIntention", { default: "求职意向" })}
            </h2>
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border-2 border-green-200 pdf-item">
              {data.jobIntention.position && (
                <div className={`font-bold text-green-700 mb-3 text-center ${isExport ? 'text-lg' : 'text-xl'}`}>
                  {data.jobIntention.position}
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                {data.jobIntention.targetSalary && (
                  <div className="bg-white rounded-lg p-3 border border-green-200">
                    <div className={`font-semibold text-green-600 ${isExport ? 'text-sm' : 'text-base'}`}>{t("cvbuilder.expectedSalary", { default: "期望薪资" })}</div>
                    <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{data.jobIntention.targetSalary}</div>
                  </div>
                )}
                {data.jobIntention.preferredLocation && (
                  <div className="bg-white rounded-lg p-3 border border-green-200">
                    <div className={`font-semibold text-green-600 ${isExport ? 'text-sm' : 'text-base'}`}>{t("cvbuilder.preferredLocation", { default: "期望地点" })}</div>
                    <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{data.jobIntention.preferredLocation}</div>
                  </div>
                )}
                {data.jobIntention.availability && (
                  <div className="bg-white rounded-lg p-3 border border-green-200">
                    <div className={`font-semibold text-green-600 ${isExport ? 'text-sm' : 'text-base'}`}>{t("cvbuilder.availability", { default: "Availability" })}</div>
                    <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{data.jobIntention.availability}</div>
                  </div>
                )}
              </div>
              {data.jobIntention.description && (
                <div className={`text-gray-700 leading-relaxed mt-4 text-center ${isExport ? 'text-sm' : 'text-base'}`}>
                  {data.jobIntention.description}
                </div>
              )}
            </div>
          </section>
        )}

        {/* Skills */}
        {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
          <section className="mb-10 pdf-section">
            <h2 className={`font-bold text-center mb-6 text-blue-600 ${isExport ? 'text-xl' : 'text-2xl'}`}>
              {t("cvbuilder.skills", { default: "技术栈" })}
            </h2>
            <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
              {(data.skills || []).map((skill: any, index: number) => (
                <div key={skill.id || index} className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 text-center border-2 border-blue-200 pdf-item">
                  <div className={`font-bold text-blue-700 ${isExport ? 'text-base' : 'text-lg'}`}>{skill.name}</div>
                  {skill.level && (
                    <Badge className={`bg-blue-100 text-blue-800 mt-2 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                      {skill.level}
                    </Badge>
                  )}
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full" style={{width: '85%'}}></div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Profile */}
        {sectionsVisible.profile !== false && data.profile?.summary && (
          <section className="mb-10 pdf-section">
            <h2 className={`font-bold text-center mb-6 text-purple-600 ${isExport ? 'text-xl' : 'text-2xl'}`}>
              {t("cvbuilder.personalProfile", { default: "开发理念" })}
            </h2>
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-6 border-2 border-purple-200 pdf-item">
              <div className={`text-gray-700 leading-relaxed text-center ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.profile.summary}
              </div>
            </div>
          </section>
        )}

        <div className={`grid gap-8 ${isExport ? 'grid-cols-2' : 'grid-cols-1 lg:grid-cols-2'}`}>
          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold mb-6 text-orange-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
                <span className="w-2 h-8 bg-orange-500 mr-3 rounded"></span>
                {t("cvbuilder.workExperience", { default: "项目经历" })}
              </h2>
              {(data.work || []).map((work: any, index: number) => (
                <div key={work.id || index} className="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-4 mb-4 border-2 border-orange-200 pdf-item">
                  <h3 className={`font-bold text-orange-700 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                  <div className={`text-red-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                  {work.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{work.location}</div>}
                  <div className={`text-gray-500 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                  </div>
                  {work.description && (
                    <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                      {work.description}
                    </div>
                  )}
                </div>
              ))}
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold mb-6 text-green-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
                <span className="w-2 h-8 bg-green-500 mr-3 rounded"></span>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              {(data.education || []).map((edu: any, index: number) => (
                <div key={edu.id || index} className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 mb-4 border-2 border-green-200 pdf-item">
                  <h3 className={`font-bold text-green-700 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                  <div className={`text-emerald-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                  {edu.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</div>}
                  {edu.gpa && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</div>}
                  <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                  </div>
                  {edu.description && (
                    <div className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                      {edu.description}
                    </div>
                  )}
                </div>
              ))}
            </section>
          )}
        </div>

        {/* Projects */}
        {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-indigo-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-indigo-500 mr-3 rounded"></span>
              {t("cvbuilder.projects", { default: "项目作品" })}
            </h2>
            <div className={`grid gap-4 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
              {(data.projects || []).map((project: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border-2 border-indigo-200 pdf-item">
                  <h3 className={`font-bold text-indigo-700 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                  {project.url && <div className={`text-purple-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</div>}
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                    {project.description}
                  </div>
                  {project.technologies && project.technologies.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {project.technologies.map((tech: string, techIndex: number) => (
                        <Badge key={techIndex} className={`bg-indigo-100 text-indigo-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Languages */}
        {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-teal-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-teal-500 mr-3 rounded"></span>
              {t("cvbuilder.languages", { default: "语言能力" })}
            </h2>
            <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
              {(data.languages || []).map((lang: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-lg p-4 text-center border-2 border-teal-200 pdf-item">
                  <div className={`font-bold text-teal-700 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                  <div className={`text-cyan-600 mt-1 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Awards */}
        {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-yellow-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-yellow-500 mr-3 rounded"></span>
              {t("cvbuilder.awards", { default: "获奖荣誉" })}
            </h2>
            <div className="space-y-4">
              {(data.awards || []).map((award: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-lg p-4 border-2 border-yellow-200 pdf-item">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className={`font-bold text-yellow-700 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                      <div className={`text-amber-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                    </div>
                    {award.date && (
                      <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Certifications */}
        {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-pink-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-pink-500 mr-3 rounded"></span>
              {t("cvbuilder.certifications", { default: "证书" })}
            </h2>
            <div className="space-y-4">
              {(data.certifications || []).map((cert: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-pink-50 to-rose-50 rounded-lg p-4 border-2 border-pink-200 pdf-item">
                  <h3 className={`font-bold text-pink-700 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                  <div className={`text-rose-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</div>
                  {cert.date && (
                    <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Interests */}
        {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-violet-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-violet-500 mr-3 rounded"></span>
              {t("cvbuilder.interests", { default: "兴趣爱好" })}
            </h2>
            <div className="flex flex-wrap gap-3">
              {(data.interests || []).map((interest: any, index: number) => (
                <span key={interest.id || index} className={`px-4 py-2 bg-gradient-to-r from-violet-100 to-purple-100 text-violet-800 rounded-full border border-violet-200 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {interest.name || interest}
                </span>
              ))}
            </div>
          </section>
        )}

        {/* Volunteer Experience */}
        {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-emerald-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-emerald-500 mr-3 rounded"></span>
              {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
            </h2>
            <div className="space-y-4">
              {(data.volunteer || []).map((vol: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-lg p-4 border-2 border-emerald-200 pdf-item">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className={`font-bold text-emerald-700 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                    <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                      {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                    </span>
                  </div>
                  <div className={`text-green-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                  {vol.description && (
                    <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.description}</div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Publications */}
        {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-slate-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-slate-500 mr-3 rounded"></span>
              {t("cvbuilder.publications", { default: "发表作品" })}
            </h2>
            <div className="space-y-4">
              {(data.publications || []).map((pub: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-lg p-4 border-2 border-slate-200 pdf-item">
                  <h3 className={`font-bold text-slate-700 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                  <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                  {pub.date && (
                    <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* References */}
        {sectionsVisible.references !== false && (data.references || []).length > 0 && (
          <section className="mt-10 pdf-section">
            <h2 className={`font-bold mb-6 text-cyan-600 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
              <span className="w-2 h-8 bg-cyan-500 mr-3 rounded"></span>
              {t("cvbuilder.references", { default: "推荐人" })}
            </h2>
            <div className={`grid gap-4 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
              {(data.references || []).map((ref: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-lg p-4 border-2 border-cyan-200 pdf-item">
                  <h3 className={`font-bold text-cyan-700 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                  <div className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                  <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                  {ref.phone && (
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* 底部装饰 */}
        <div className="text-center mt-10">
          <div className={`inline-block bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 px-6 py-3 rounded-full border-2 border-blue-200 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
            Ready to build the future 🚀
          </div>
        </div>
      </div>
    </div>
  );
}
