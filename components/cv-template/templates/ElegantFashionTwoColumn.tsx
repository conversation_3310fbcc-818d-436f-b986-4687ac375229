"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ElegantFashionTwoColumnProps {
  data?: any;
  isExport?: boolean;
}

export default function ElegantFashionTwoColumn({
  data: propData,
  isExport = false
}: ElegantFashionTwoColumnProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 优雅时尚双栏风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '30px' : '40px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1f2937',
    boxShadow: isExport ? 'none' : '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Georgia, serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <div className="text-center py-8 border-b border-gray-200 mb-8 pdf-section">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-6"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-pink-100 to-rose-100 text-pink-600 font-light"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-serif font-bold text-gray-900 tracking-widest mb-2 ${isExport ? 'text-3xl' : 'text-4xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <p className={`text-pink-500 mb-3 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {data.jobIntention.position}
          </p>
        )}
        <div className="flex justify-center space-x-8 text-gray-500">
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
        </div>
        {(data.personal?.website || data.personal?.linkedin || data.personal?.github) && (
          <div className="flex justify-center space-x-6 mt-3 text-gray-500">
            {data.personal?.website && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
            )}
            {data.personal?.linkedin && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
            )}
            {data.personal?.github && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
            )}
          </div>
        )}
      </div>
      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`pdf-item p-4 bg-pink-50 rounded-lg border-l-4 border-pink-500`}>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.expectedSalary", { default: "期望薪资" })}:</span> {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "期望地点" })}:</span> {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Two Column Layout */}
      <div className={`grid gap-8 ${isExport ? 'grid-cols-2' : 'grid-cols-1 lg:grid-cols-2'}`}>
        {/* Left Column */}
        <div className="space-y-8">
          {/* Profile */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.personalProfile", { default: "个人简介" })}
              </h2>
              <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{data.profile.summary}</p>
            </div>
          )}

          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.skills", { default: "专业技能" })}
              </h2>
              <div className="flex flex-wrap gap-3">
                {(data.skills || []).map((skill: any, index: number) => (
                  <span key={skill.id || index} className={`px-4 py-2 bg-pink-50 text-pink-800 rounded-lg font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
                    {skill.name}
                    {skill.level && (
                      <Badge className={`ml-2 bg-pink-100 text-pink-700 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {skill.level}
                      </Badge>
                    )}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-3">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className={`text-gray-800 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                    <span className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-3 py-1 bg-pink-100 text-pink-700 rounded-full ${isExport ? 'text-sm' : 'text-base'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index}>
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                    <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</p>
                    {cert.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-6">
                {(data.work || []).map((job: any, index: number) => (
                  <div key={job.id || index}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{job.position}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-pink-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{job.company}</p>
                    {job.location && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{job.location}</p>}
                    {job.description && <p className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{job.description}</p>}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-6">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{project.name}</h3>
                      {project.startDate && project.endDate && (
                        <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                          {project.startDate} - {project.endDate}
                        </span>
                      )}
                    </div>
                    {project.url && <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</p>}
                    <p className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{project.description}</p>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} className={`bg-pink-100 text-pink-700 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-6">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={edu.id || index}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-pink-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                    {edu.location && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</p>}
                    {edu.gpa && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</p>}
                    {edu.description && <p className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{edu.description}</p>}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-4">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="flex justify-between items-start">
                    <div>
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                      <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</p>
                    </div>
                    {award.date && (
                      <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-4">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</p>
                    {vol.description && <p className={`text-gray-700 mt-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.description}</p>}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-4">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index}>
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                    <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</p>
                    {pub.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <div className="pdf-section pdf-item">
              <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-pink-500 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="space-y-4">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index}>
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                    <p className={`text-pink-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</p>
                    <p className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</p>
                    {ref.phone && (
                      <p className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}