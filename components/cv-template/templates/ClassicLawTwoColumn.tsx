"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ClassicLawTwoColumnProps {
  data?: any;
  isExport?: boolean;
}

export default function ClassicLawTwoColumn({
  data: propData,
  isExport = false
}: ClassicLawTwoColumnProps) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1a1a1a',
    boxShadow: isExport ? 'none' : '0 4px 24px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.6',
    fontFamily: 'Times New Roman, serif',
  };
  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      <div className={`grid gap-8 ${isExport ? 'grid-cols-5' : 'grid-cols-1 lg:grid-cols-5'}`}>
        {/* Left Sidebar */}
        <aside className={`bg-gray-50 p-6 rounded-lg ${isExport ? 'col-span-2' : 'lg:col-span-2'}`}>
          {/* Personal Info */}
          <div className="text-center mb-6">
            {data.personal?.avatar && (
              <div className="flex justify-center mb-4">
                <img
                  src={data.personal.avatar}
                  alt="Profile"
                  className={`rounded-full object-cover ${isExport ? 'w-16 h-16' : 'w-20 h-20'}`}
                />
              </div>
            )}
            <h1 className={`font-bold text-gray-900 mb-2 ${isExport ? 'text-xl' : 'text-2xl'}`}>
              {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
            </h1>
            {data.jobIntention?.position && (
              <div className={`text-gray-600 font-medium mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {data.jobIntention.position}
              </div>
            )}
            <div className={`text-gray-600 space-y-2 ${isExport ? 'text-sm' : 'text-base'}`}>
              <div>📧 {data.personal?.email || "<EMAIL>"}</div>
              <div>📱 {data.personal?.phone || "+86 138 0000 0000"}</div>
              <div>📍 {data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</div>
              {data.personal?.website && (
                <div className="text-blue-600">🌐 {data.personal.website}</div>
              )}
              {data.personal?.linkedin && (
                <div className="text-blue-600">💼 {data.personal.linkedin}</div>
              )}
              {data.personal?.github && (
                <div className="text-blue-600">💻 {data.personal.github}</div>
              )}
            </div>
          </div>

          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <section className="mb-6 pdf-section">
              <h2 className={`font-bold text-gray-800 mb-3 border-b border-gray-300 pb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.skills", { default: "专业技能" })}
              </h2>
              <div className="space-y-2">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="pdf-item">
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>
                      • {skill.name}
                      {skill.level && (
                        <span className={`ml-2 text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>
                          ({skill.level})
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <section className="mb-6 pdf-section">
              <h2 className={`font-bold text-gray-800 mb-3 border-b border-gray-300 pb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-2">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="pdf-item flex justify-between">
                    <span className={`text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                    <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="mb-6 pdf-section">
              <h2 className={`font-bold text-gray-800 mb-3 border-b border-gray-300 pb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</div>
                    {cert.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-3 border-b border-gray-300 pb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-2 py-1 bg-gray-200 text-gray-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </section>
          )}
        </aside>

        {/* Main Content */}
        <main className={`${isExport ? 'col-span-3' : 'lg:col-span-3'} space-y-8`}>
          {/* Job Intention */}
          {sectionsVisible.jobIntention !== false && data.jobIntention && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.jobIntention", { default: "求职意向" })}
              </h2>
              <div className={`pdf-item bg-gray-50 p-4 rounded border-l-4 border-gray-600`}>
                {data.jobIntention.position && (
                  <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                    {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {data.jobIntention.targetSalary && (
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                      <span className="font-medium">{t("cvbuilder.targetSalary", { default: "Target Salary" })}:</span> {data.jobIntention.targetSalary}
                    </div>
                  )}
                  {data.jobIntention.preferredLocation && (
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                      <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "Preferred Location" })}:</span> {data.jobIntention.preferredLocation}
                    </div>
                  )}
                </div>
                {data.jobIntention.description && (
                  <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {data.jobIntention.description}
                  </div>
                )}
              </div>
            </section>
          )}

          {/* Profile */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.personalProfile", { default: "个人简介" })}
              </h2>
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item`}>
                {data.profile.summary}
              </div>
            </section>
          )}

          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              {(data.work || []).map((work: any, index: number) => (
                <div key={work.id || index} className="mb-6 pdf-item">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                      <div className={`text-gray-700 font-semibold ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                      {work.location && (
                        <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{work.location}</div>
                      )}
                    </div>
                    <div className={`text-gray-500 text-right ${isExport ? 'text-sm' : 'text-base'}`} style={{ minWidth: '120px' }}>
                      {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                    </div>
                  </div>
                  {work.description && (
                    <div className={`text-gray-700 leading-relaxed pl-4 border-l-2 border-gray-300 ${isExport ? 'text-sm' : 'text-base'}`}>
                      {work.description}
                    </div>
                  )}
                </div>
              ))}
            </section>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-4">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gray-50 rounded border-l-4 border-gray-600">
                    <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                    {project.url && (
                      <div className={`text-blue-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</div>
                    )}
                    {project.startDate && project.endDate && (
                      <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {project.startDate} - {project.endDate}
                      </div>
                    )}
                    {project.description && (
                      <div className={`text-gray-700 leading-relaxed mb-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {project.description}
                      </div>
                    )}
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <span key={techIndex} className={`px-2 py-1 bg-gray-200 text-gray-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              {(data.education || []).map((edu: any, index: number) => (
                <div key={edu.id || index} className="mb-4 pdf-item">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                      <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                      {edu.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</div>}
                      {edu.gpa && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</div>}
                    </div>
                    <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                      {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                    </div>
                  </div>
                  {edu.description && (
                    <div className={`text-gray-700 leading-relaxed pl-4 border-l-2 border-gray-300 ${isExport ? 'text-sm' : 'text-base'}`}>
                      {edu.description}
                    </div>
                  )}
                </div>
              ))}
            </section>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-4">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gray-50 rounded border-l-4 border-yellow-500">
                    <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{award.name}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                    {award.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-4">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gray-50 rounded border-l-4 border-green-500">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{vol.organization}</h3>
                        <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                      </div>
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </div>
                    </div>
                    {vol.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {vol.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-4">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gray-50 rounded border-l-4 border-purple-500">
                    <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{pub.title}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                    {pub.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b border-gray-300 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gray-50 rounded border-l-4 border-indigo-500">
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{ref.name}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                    {ref.phone && (
                      <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </main>
      </div>
    </div>
  );
}