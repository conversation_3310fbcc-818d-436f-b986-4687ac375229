"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ElegantLuxurySingleProps {
  data?: any;
  isExport?: boolean;
}

export default function ElegantLuxurySingle({
  data: propData,
  isExport = false
}: ElegantLuxurySingleProps) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  const containerStyle = isExport ? {
    fontSize: '12px',
    lineHeight: '1.4',
    padding: '40px',
    maxWidth: '210mm',
    minHeight: '297mm',
    fontFamily: 'PingFang SC, Microsoft YaHei, SimSun, sans-serif'
  } : {};

  return (
    <div className="pdf-container bg-white text-gray-800 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="pdf-section pdf-item text-center py-8 border-b border-gray-300 mb-8">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className={`mx-auto rounded-full object-cover mb-4 ${isExport ? 'w-20 h-20' : 'w-24 h-24'}`}
          />
        )}
        <h1 className={`font-serif font-bold text-gray-900 tracking-widest mb-2 ${isExport ? 'text-3xl' : 'text-4xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <p className={`text-gray-500 mb-3 ${isExport ? 'text-lg' : 'text-xl'}`}>{data.jobIntention.position}</p>
        )}
        <div className={`flex justify-center flex-wrap gap-6 text-gray-400 ${isExport ? 'text-sm' : 'text-base'}`}>
          <span>{data.personal?.email || "<EMAIL>"}</span>
          <span>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && <span>{data.personal.website}</span>}
          {data.personal?.linkedin && <span>LinkedIn: {data.personal.linkedin}</span>}
          {data.personal?.github && <span>GitHub: {data.personal.github}</span>}
        </div>
      </header>
      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400`}>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "期望职位" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.expectedSalary", { default: "期望薪资" })}:</span> {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "期望地点" })}:</span> {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.personalProfile", { default: "个人简介" })}
          </h2>
          <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-lg'}`}>{data.profile.summary}</p>
        </section>
      )}

      {/* Work Experience */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.workExperience", { default: "工作经历" })}
          </h2>
          <div className="space-y-6">
            {(data.work || []).map((job: any, index: number) => (
              <div key={job.id || index} className="pdf-item">
                <div className="flex justify-between items-center mb-1">
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{job.position}</h3>
                  <span className={`text-gray-400 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                  </span>
                </div>
                <p className={`text-gray-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{job.company}</p>
                {job.location && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{job.location}</p>}
                {job.description && <p className={`text-gray-700 mt-1 ${isExport ? 'text-sm' : 'text-base'}`}>{job.description}</p>}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Education */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          <div className="space-y-6">
            {(data.education || []).map((edu: any, index: number) => (
              <div key={edu.id || index} className="pdf-item">
                <div className="flex justify-between items-center mb-1">
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                  <span className={`text-gray-400 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                  </span>
                </div>
                <p className={`text-gray-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                {edu.location && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</p>}
                {edu.gpa && <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</p>}
                {edu.description && <p className={`text-gray-700 mt-1 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.description}</p>}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.skills", { default: "专业技能" })}
          </h2>
          <div className="flex flex-wrap gap-3">
            {(data.skills || []).map((skill: any, index: number) => (
              <span key={skill.id || index} className={`px-4 py-2 bg-yellow-50 text-yellow-800 rounded-lg font-medium ${isExport ? 'text-sm' : 'text-base'}`}>
                {skill.name}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Projects */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="space-y-6">
            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                {project.url && <p className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</p>}
                {project.startDate && project.endDate && (
                  <p className={`text-gray-500 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {project.startDate} - {project.endDate}
                  </p>
                )}
                {project.description && (
                  <p className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{project.description}</p>
                )}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {project.technologies.map((tech: string, techIndex: number) => (
                      <span key={techIndex} className={`px-2 py-1 bg-yellow-100 text-yellow-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {tech}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-3'}`}>
            {(data.languages || []).map((lang: any, index: number) => (
              <div key={index} className="text-center p-3 bg-gray-50 rounded pdf-item">
                <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className="space-y-4">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</p>
                {cert.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className="space-y-4">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</p>
                {award.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-3">
            {(data.interests || []).map((interest: any, index: number) => (
              <span key={interest.id || index} className={`px-3 py-1 bg-yellow-100 text-yellow-700 rounded ${isExport ? 'text-sm' : 'text-base'}`}>
                {interest.name || interest}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-4">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="pdf-item">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                  </div>
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                  </div>
                </div>
                {vol.description && (
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                    {vol.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="pdf-section pdf-item mb-8">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-4">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                {pub.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section pdf-item">
          <h2 className={`font-semibold text-gray-900 mb-4 border-l-4 border-yellow-400 pl-3 ${isExport ? 'text-base' : 'text-lg'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className={`grid gap-6 ${isExport ? 'grid-cols-1' : 'grid-cols-2'}`}>
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                {ref.phone && (
                  <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}