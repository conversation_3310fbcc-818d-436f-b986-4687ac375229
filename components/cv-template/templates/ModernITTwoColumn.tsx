"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ModernITTwoColumnProps {
  data?: any;
  isExport?: boolean;
}

export default function ModernITTwoColumn({
  data: propData,
  isExport = false
}: ModernITTwoColumnProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#000',
    boxShadow: isExport ? 'none' : '0 4px 24px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900" style={containerStyle}>
      <div className={`grid gap-8 ${isExport ? 'grid-cols-3' : 'grid-cols-1 lg:grid-cols-3'}`}>
        {/* Left Sidebar */}
        <aside className={`bg-gradient-to-b from-blue-50 to-indigo-100 p-6 rounded-lg ${isExport ? 'col-span-1' : 'lg:col-span-1'}`}>
          {/* Avatar */}
          <div className="text-center mb-6">
            {data.personal?.avatar && (
              <img
                src={data.personal.avatar}
                alt={data.personal.name || 'Avatar'}
                className="pdf-avatar mx-auto mb-4"
                style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
              />
            )}
            {!data.personal?.avatar && (
              <div
                className="pdf-avatar mx-auto mb-4 flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold"
                style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
              >
                {data.personal?.name?.[0]?.toUpperCase() || '?'}
              </div>
            )}

            <h1 className={`font-bold text-gray-900 mb-2 ${isExport ? 'text-xl' : 'text-2xl'}`}>
              {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
            </h1>
            {data.jobIntention?.position && (
              <div className={`text-blue-600 font-semibold mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {data.jobIntention.position}
              </div>
            )}
          </div>

          {/* Contact Info */}
          <div className="mb-6">
            <h2 className={`font-bold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-sm' : 'text-base'}`}>
              {t("cvbuilder.contactInfo", { default: "联系方式" })}
            </h2>
            <div className="space-y-2">
              <div className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>📧 {data.personal?.email || "<EMAIL>"}</div>
              <div className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>📱 {data.personal?.phone || "+86 138 0000 0000"}</div>
              <div className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>📍 {data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</div>
              {data.personal?.website && (
                <div className={`text-blue-600 ${isExport ? 'text-xs' : 'text-sm'}`}>🌐 {data.personal.website}</div>
              )}
            </div>
          </div>
          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <div className="mb-6">
              <h2 className={`font-bold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.skills", { default: "技能" })}
              </h2>
              <div className="space-y-2">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="pdf-item">
                    <div className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{skill.name}</div>
                    {skill.level && (
                      <Badge variant="secondary" className={`mt-1 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {skill.level}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <div className="mb-6">
              <h2 className={`font-bold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-2">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <div className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.language}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <div className="mb-6">
              <h2 className={`font-bold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-1">
                {(data.interests || []).map((interest: any, index: number) => (
                  <Badge key={interest.id || index} variant="secondary" className={`bg-blue-100 text-blue-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                    {interest.name || interest}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </aside>

        {/* Main Content */}
        <main className={`space-y-8 ${isExport ? 'col-span-2' : 'lg:col-span-2'}`}>
          {/* Job Intention */}
          {sectionsVisible.jobIntention !== false && data.jobIntention && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.jobIntention", { default: "求职意向" })}
              </h2>
              <div className={`pdf-item bg-green-50 p-4 rounded-lg border-l-4 border-green-500`}>
                {data.jobIntention.position && (
                  <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                    {t("cvbuilder.expectedPosition", { default: "期望职位" })}: {data.jobIntention.position}
                  </div>
                )}
                {data.jobIntention.targetSalary && (
                  <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {t("cvbuilder.expectedSalary", { default: "期望薪资" })}: {data.jobIntention.targetSalary}
                  </div>
                )}
                {data.jobIntention.preferredLocation && (
                  <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {t("cvbuilder.preferredLocation", { default: "期望地点" })}: {data.jobIntention.preferredLocation}
                  </div>
                )}
                {data.jobIntention.description && (
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                    {data.jobIntention.description}
                  </div>
                )}
              </div>
            </section>
          )}

          {/* Profile */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.personalProfile", { default: "个人简介" })}
              </h2>
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500`}>
                {data.profile.summary}
              </div>
            </section>
          )}

          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-6">
                {(data.work || []).map((work: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-l-4 border-blue-500">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                        <div className={`text-blue-600 font-semibold ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                        {work.location && (
                          <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{work.location}</div>
                        )}
                      </div>
                      <div className={`text-gray-500 text-right ${isExport ? 'text-xs' : 'text-sm'}`} style={{ minWidth: '120px' }}>
                        {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                      </div>
                    </div>
                    {work.description && (
                      <div className={`text-gray-700 leading-relaxed pl-4 border-l-2 border-blue-300 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {work.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-4">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg border-l-4 border-indigo-500">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                      </span>
                    </div>
                    {project.url && (
                      <p className={`text-blue-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                    )}
                    <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} variant="secondary" className={`bg-indigo-100 text-indigo-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-4">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border-l-4 border-green-500">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                        <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                        {edu.location && <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</div>}
                        {edu.gpa && <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</div>}
                      </div>
                      <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                      </div>
                    </div>
                    {edu.description && (
                      <div className={`text-gray-700 mt-2 leading-relaxed pl-4 border-l-2 border-green-300 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {edu.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="pdf-item p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border-l-4 border-yellow-500">
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                    {cert.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.awards", { default: "获奖情况" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="pdf-item p-3 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg border-l-4 border-red-500">
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                    {award.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-4">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="pdf-item p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg border-l-4 border-emerald-500">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{vol.organization}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-blue-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</p>
                    {vol.description && (
                      <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-3">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="pdf-item p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border-l-4 border-orange-500">
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                    {pub.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="pdf-item p-3 bg-gradient-to-r from-slate-50 to-gray-50 rounded-lg border border-slate-200">
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                    {ref.phone && (
                      <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </main>
      </div>
    </div>
  );
}