"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ModernStartupSingleProps {
  data?: any;
  isExport?: boolean;
}

export default function ModernStartupSingle({
  data: propData,
  isExport = false
}: ModernStartupSingleProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();



  // PDF导出样式调整
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#000',
    boxShadow: isExport ? 'none' : '0 4px 24px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };
  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <div className="text-center mb-8 border-b-2 border-gradient-to-r from-blue-500 to-purple-600 pb-6">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-4"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-4 flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-bold text-gray-900 mb-3 ${isExport ? 'text-2xl' : 'text-3xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <div className={`text-blue-600 font-semibold mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {data.jobIntention.position}
          </div>
        )}
        <div className="flex justify-center flex-wrap gap-4 text-gray-600">
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && (
            <span className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
          )}
          {data.personal?.linkedin && (
            <span className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
          )}
          {data.personal?.github && (
            <span className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
          )}
        </div>
      </div>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`pdf-item bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-green-500`}>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "期望职位" })}: {data.jobIntention.position}
              </div>
            )}
            {data.jobIntention.targetSalary && (
              <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.expectedSalary", { default: "期望薪资" })}: {data.jobIntention.targetSalary}
              </div>
            )}
            {data.jobIntention.preferredLocation && (
              <div className={`text-gray-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.preferredLocation", { default: "期望地点" })}: {data.jobIntention.preferredLocation}
              </div>
            )}
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.personalProfile", { default: "个人简介" })}
          </h2>
          <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-blue-500`}>
            {data.profile.summary}
          </div>
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.skills", { default: "核心技能" })}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {(data.skills || []).map((skill: any, index: number) => (
              <div key={skill.id || index} className="pdf-item p-3 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 hover:shadow-md transition-shadow">
                <div className={`font-semibold text-gray-800 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</div>
                {skill.level && (
                  <Badge variant="secondary" className={`${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                    {skill.level}
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Work Experience */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.workExperience", { default: "工作经历" })}
          </h2>
          {(data.work || []).map((work: any, index: number) => (
            <div key={index} className="mb-6 pdf-item p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border-l-4 border-green-500">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                  <div className={`text-blue-600 font-semibold ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                  {work.location && (
                    <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{work.location}</div>
                  )}
                </div>
                <div className={`text-gray-500 text-right ${isExport ? 'text-xs' : 'text-sm'}`} style={{ minWidth: '120px' }}>
                  {formatDateRange(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth, locale, t("cvbuilder.present", { default: "Present" }))}
                </div>
              </div>
              {work.description && (
                <div className={`text-gray-700 leading-relaxed pl-4 border-l-2 border-green-300 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {work.description}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* Projects */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="space-y-4">
            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className="pdf-item p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border-l-4 border-purple-500">
                <div className="flex justify-between items-start mb-2">
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                  <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                  </span>
                </div>
                {project.url && (
                  <p className={`text-blue-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                )}
                <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                {project.technologies && project.technologies.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {project.technologies.map((tech: string, techIndex: number) => (
                      <Badge key={techIndex} variant="secondary" className={`${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {tech}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Education */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          {(data.education || []).map((edu: any, index: number) => (
            <div key={index} className="mb-4 pdf-item p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg border-l-4 border-indigo-500">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.school}</h3>
                  <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                  {edu.location && <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</div>}
                  {edu.gpa && <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</div>}
                </div>
                <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {formatDateRange(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth, locale, t("cvbuilder.present", { default: "Present" }))}
                </div>
              </div>
              {edu.description && (
                <div className={`text-gray-700 mt-2 leading-relaxed pl-4 border-l-2 border-indigo-300 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {edu.description}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className="grid grid-cols-2 gap-4">
            {(data.languages || []).map((lang: any, index: number) => (
              <div key={index} className="pdf-item p-3 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg border border-teal-200">
                <div className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className="space-y-3">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="pdf-item p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border-l-4 border-yellow-500">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                {cert.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.awards", { default: "获奖情况" })}
          </h2>
          <div className="space-y-3">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="pdf-item p-3 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg border-l-4 border-red-500">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                {award.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-2">
            {(data.interests || []).map((interest: any, index: number) => (
              <Badge key={interest.id || index} variant="secondary" className={`bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 ${isExport ? 'text-xs px-2 py-1' : 'text-sm px-3 py-1'}`}>
                {interest.name || interest}
              </Badge>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-4">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="pdf-item p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg border-l-4 border-emerald-500">
                <div className="flex justify-between items-start mb-2">
                  <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{vol.organization}</h3>
                  <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRange(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth, locale, t("cvbuilder.present", { default: "Present" }))}
                  </span>
                </div>
                <p className={`text-blue-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</p>
                {vol.description && (
                  <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-3">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="pdf-item p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border-l-4 border-orange-500">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                {pub.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section">
          <h2 className={`font-bold text-gray-800 mb-4 border-b-2 border-blue-500 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="pdf-item p-3 bg-gradient-to-r from-slate-50 to-gray-50 rounded-lg border border-slate-200">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                {ref.phone && (
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}