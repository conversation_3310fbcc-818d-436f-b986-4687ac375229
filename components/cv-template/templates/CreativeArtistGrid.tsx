"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface CreativeArtistGridProps {
  data?: any;
  isExport?: boolean;
}

export default function CreativeArtistGrid({
  data: propData,
  isExport = false
}: CreativeArtistGridProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 创意艺术家风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#2d3748',
    boxShadow: isExport ? 'none' : '0 8px 32px rgba(0,0,0,0.12)',
    borderRadius: isExport ? '0' : '12px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };
  return (
    <div className="pdf-container bg-white text-gray-800 mx-auto" style={containerStyle}>
      {/* Header Section with Creative Design */}
      <div className="pdf-section pdf-item bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6 mb-6 rounded-lg">
        <div className="text-center">
          {data.personal?.avatar && (
            <img
              src={data.personal.avatar}
              alt={data.personal.name || 'Avatar'}
              className={`mx-auto rounded-full object-cover mb-4 border-4 border-white ${isExport ? 'w-16 h-16' : 'w-20 h-20'}`}
            />
          )}
          <h1 className={`font-bold mb-2 ${isExport ? 'text-2xl' : 'text-4xl'}`}>
            {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
          </h1>
          {data.jobIntention?.position && (
            <p className={`mb-3 opacity-90 ${isExport ? 'text-lg' : 'text-xl'}`}>{data.jobIntention.position}</p>
          )}
          <div className={`flex justify-center flex-wrap gap-4 opacity-80 ${isExport ? 'text-sm' : 'text-base'}`}>
            <span>{data.personal?.email || "<EMAIL>"}</span>
            <span>{data.personal?.phone || "+86 138 0000 0000"}</span>
            <span>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
            {data.personal?.website && <span>{data.personal.website}</span>}
            {data.personal?.linkedin && <span>LinkedIn: {data.personal.linkedin}</span>}
            {data.personal?.github && <span>GitHub: {data.personal.github}</span>}
          </div>
        </div>
      </div>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-6 pdf-section">
          <div className="pdf-item p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border-l-4 border-purple-600">
            <h2 className={`font-bold text-gray-900 mb-3 flex items-center ${isExport ? 'text-lg' : 'text-xl'}`}>
              <span className="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>
              {t("cvbuilder.jobIntention", { default: "求职意向" })}
            </h2>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.targetSalary", { default: "Target Salary" })}: {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.preferredLocation", { default: "Preferred Location" })}: {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile Section */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-6 pdf-section">
          <div className="pdf-item p-4 bg-gray-50 rounded-lg">
            <h2 className={`font-bold text-gray-900 mb-3 flex items-center ${isExport ? 'text-lg' : 'text-xl'}`}>
              <span className="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>
              {t("cvbuilder.personalProfile", { default: "个人简介" })}
            </h2>
            <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{data.profile.summary}</p>
          </div>
        </section>
      )}

      {/* Grid Layout */}
      <div className={`grid gap-6 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
        {/* Left Column */}
        <div className="space-y-6">
          {/* Skills Section */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.skills", { default: "专业技能" })}
              </h2>
              <div className="space-y-3">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <div className="flex justify-between items-center mb-1">
                      <span className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</span>
                      {skill.level && (
                        <span className={`text-purple-600 font-medium ${isExport ? 'text-xs' : 'text-sm'}`}>{skill.level}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Education Section */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-pink-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-4">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={edu.id || index} className="relative pl-4 border-l-2 border-pink-300">
                    <div className="absolute left-[-6px] top-2 w-3 h-3 bg-pink-500 rounded-full"></div>
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
                    <p className={`text-gray-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                    {edu.location && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</p>}
                    {edu.gpa && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</p>}
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                    </p>
                    {edu.description && (
                      <p className={`text-gray-700 mt-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-2">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="flex justify-between items-center bg-gradient-to-r from-purple-50 to-pink-50 p-2 rounded">
                    <span className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                    <span className={`text-purple-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-pink-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Work Experience Section */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-4">
                {(data.work || []).map((job: any, index: number) => (
                  <div key={job.id || index} className="relative pl-4 border-l-2 border-purple-300">
                    <div className="absolute left-[-6px] top-2 w-3 h-3 bg-purple-500 rounded-full"></div>
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{job.position}</h3>
                      <span className={`text-purple-600 font-medium ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-gray-600 font-medium mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{job.company}</p>
                    {job.location && <p className={`text-gray-500 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.location}</p>}
                    {job.description && (
                      <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-pink-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-4">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <h3 className={`font-semibold text-gray-900 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{project.name}</h3>
                    {project.url && <p className={`text-purple-600 mb-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>}
                    {project.startDate && project.endDate && (
                      <p className={`text-gray-500 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {project.startDate} - {project.endDate}
                      </p>
                    )}
                    {project.description && (
                      <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                    )}
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <span key={techIndex} className={`px-2 py-1 bg-purple-200 text-purple-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                    {cert.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-pink-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-3">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                    {award.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-3">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                      <span className={`text-purple-600 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</p>
                    {vol.description && (
                      <p className={`text-gray-700 mt-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-pink-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-3">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                    {pub.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <div className="pdf-section pdf-item p-4 border-l-4 border-purple-600 bg-white">
              <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="space-y-3">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 rounded-lg">
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                    {ref.phone && (
                      <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}