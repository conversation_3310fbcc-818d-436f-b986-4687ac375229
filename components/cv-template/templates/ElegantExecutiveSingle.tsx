"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ElegantExecutiveSingleProps {
  data?: any;
  isExport?: boolean;
}

export default function ElegantExecutiveSingle({
  data: propData,
  isExport = false
}: ElegantExecutiveSingleProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 优雅高管风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '40px' : '48px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1a1a1a',
    boxShadow: isExport ? 'none' : '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Playfair Display, Georgia, serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="border-b-2 border-slate-300 pb-8 mb-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {data.personal?.avatar && (
              <img
                src={data.personal.avatar}
                alt={data.personal.name || 'Avatar'}
                className="pdf-avatar"
                style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover' }}
              />
            )}
            {!data.personal?.avatar && (
              <div
                className="pdf-avatar flex items-center justify-center bg-slate-100 text-slate-500 font-light"
                style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
              >
                {data.personal?.name?.[0]?.toUpperCase() || '?'}
              </div>
            )}
            <div>
              <h1 className={`font-bold text-slate-800 mb-2 ${isExport ? 'text-3xl' : 'text-4xl'}`}>
                {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
              </h1>
              {data.jobIntention?.position && (
                <div className={`text-slate-600 font-medium ${isExport ? 'text-lg' : 'text-xl'}`}>
                  {data.jobIntention.position}
                </div>
              )}
            </div>
          </div>
          <div className={`text-right text-slate-600 space-y-1 ${isExport ? 'text-sm' : 'text-base'}`}>
            <div>{data.personal?.email || "<EMAIL>"}</div>
            <div>{data.personal?.phone || "+86 138 0000 0000"}</div>
            <div>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</div>
            {data.personal?.website && <div>{data.personal.website}</div>}
            {data.personal?.linkedin && <div>LinkedIn: {data.personal.linkedin}</div>}
            {data.personal?.github && <div>GitHub: {data.personal.github}</div>}
          </div>
        </div>
      </header>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-4 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`pdf-item p-6 bg-slate-50 rounded-lg border-l-4 border-slate-600`}>
            {data.jobIntention.position && (
              <div className={`font-semibold text-slate-800 mb-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.expectedSalary", { default: "期望薪资" })}:</span> {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "期望地点" })}:</span> {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-slate-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}



      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-4 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.personalProfile", { default: "执行摘要" })}
          </h2>
          <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-slate-600 pdf-item">
            <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
              {data.profile.summary}
            </div>
          </div>
        </section>
      )}

      {/* Work Experience */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.workExperience", { default: "职业经历" })}
          </h2>
          {(data.work || []).map((work: any, index: number) => (
            <div key={work.id || index} className="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-slate-300 pdf-item">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className={`font-bold text-slate-800 ${isExport ? 'text-lg' : 'text-xl'}`}>{work.position}</h3>
                  <div className={`text-slate-600 font-medium ${isExport ? 'text-base' : 'text-lg'}`}>{work.company}</div>
                  {work.location && <div className={`text-slate-500 ${isExport ? 'text-sm' : 'text-base'}`}>{work.location}</div>}
                </div>
                <div className={`text-slate-500 bg-slate-100 px-3 py-1 rounded ${isExport ? 'text-sm' : 'text-base'}`}>
                  {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                </div>
              </div>
              {work.description && (
                <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                  {work.description}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* Education */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          <div className={`grid gap-4 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
            {(data.education || []).map((edu: any, index: number) => (
              <div key={edu.id || index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                {edu.location && <div className={`text-slate-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</div>}
                {edu.gpa && <div className={`text-slate-500 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</div>}
                <div className={`text-slate-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                </div>
                {edu.description && (
                  <div className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {edu.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.skills", { default: "核心技能" })}
          </h2>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'}`}>
              {(data.skills || []).map((skill: any, index: number) => (
                <div key={skill.id || index} className="flex items-center justify-between p-3 bg-slate-50 rounded border pdf-item">
                  <span className={`text-slate-800 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</span>
                  {skill.level && (
                    <Badge className={`bg-slate-200 text-slate-600 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                      {skill.level}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Projects */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          {(data.projects || []).map((project: any, index: number) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-slate-300 pdf-item">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className={`font-bold text-slate-800 ${isExport ? 'text-lg' : 'text-xl'}`}>{project.name}</h3>
                  {project.url && <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{project.url}</div>}
                </div>
                {project.startDate && project.endDate && (
                  <div className={`text-slate-500 bg-slate-100 px-3 py-1 rounded ${isExport ? 'text-sm' : 'text-base'}`}>
                    {project.startDate} - {project.endDate}
                  </div>
                )}
              </div>
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                {project.description}
              </div>
              {project.technologies && project.technologies.length > 0 && (
                <div className="mt-4 flex flex-wrap gap-2">
                  {project.technologies.map((tech: string, techIndex: number) => (
                    <Badge key={techIndex} className={`bg-slate-100 text-slate-700 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                      {tech}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className={`grid gap-4 ${isExport ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'}`}>
              {(data.languages || []).map((lang: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded border pdf-item">
                  <span className={`text-slate-800 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                  <span className={`text-slate-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</span>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className="space-y-4">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                    <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                  </div>
                  {award.date && (
                    <span className={`text-slate-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className="space-y-4">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</div>
                {cert.date && (
                  <div className={`text-slate-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-3">
            {(data.interests || []).map((interest: any, index: number) => (
              <span key={interest.id || index} className={`px-3 py-2 bg-slate-100 text-slate-700 rounded-lg ${isExport ? 'text-sm' : 'text-base'}`}>
                {interest.name || interest}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-4">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <div className="flex justify-between items-start mb-2">
                  <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                  <span className={`text-slate-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                    {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                  </span>
                </div>
                <div className={`text-slate-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                {vol.description && (
                  <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.description}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="mb-10 pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-4">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                {pub.date && (
                  <div className={`text-slate-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section">
          <h2 className={`font-bold text-slate-800 mb-6 flex items-center ${isExport ? 'text-xl' : 'text-2xl'}`}>
            <span className="w-1 h-8 bg-slate-600 mr-4"></span>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className={`grid gap-4 ${isExport ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}>
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-slate-200 pdf-item">
                <h3 className={`font-bold text-slate-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                {ref.phone && (
                  <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}
