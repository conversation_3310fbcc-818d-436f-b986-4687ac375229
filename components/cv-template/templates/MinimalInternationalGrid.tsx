"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface MinimalInternationalGridProps {
  data?: any;
  isExport?: boolean;
}

export default function MinimalInternationalGrid({
  data: propData,
  isExport = false
}: MinimalInternationalGridProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 极简国际风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '30px' : '40px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#333333',
    boxShadow: isExport ? 'none' : '0 1px 3px rgba(0,0,0,0.02)',
    borderRadius: isExport ? '0' : '0',
    fontSize: isExport ? '11px' : '13px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Helvetica, Arial, sans-serif',
  };
  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="text-center mb-8 border-b border-gray-300 pb-6">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-4"
            style={{ width: isExport ? 60 : 80, height: isExport ? 60 : 80, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-4 flex items-center justify-center bg-gray-200 text-gray-600 font-light"
            style={{ width: isExport ? 60 : 80, height: isExport ? 60 : 80, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 24 : 32 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-light text-gray-900 mb-2 tracking-wide ${isExport ? 'text-xl' : 'text-2xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <div className={`text-gray-600 mb-4 font-light ${isExport ? 'text-sm' : 'text-base'}`}>
            {data.jobIntention.position}
          </div>
        )}
        <div className="flex justify-center flex-wrap gap-4 text-gray-500">
          <span className={isExport ? 'text-xs' : 'text-sm'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-xs' : 'text-sm'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-xs' : 'text-sm'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && (
            <span className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{data.personal.website}</span>
          )}
          {data.personal?.linkedin && (
            <span className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>LinkedIn: {data.personal.linkedin}</span>
          )}
          {data.personal?.github && (
            <span className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GitHub: {data.personal.github}</span>
          )}
        </div>
      </header>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className={`pdf-item text-center ${isExport ? 'text-xs' : 'text-sm'}`}>
            {data.jobIntention.position && (
              <div className={`font-medium text-gray-800 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-gray-600">
              {data.jobIntention.targetSalary && (
                <div>{t("cvbuilder.expectedSalary", { default: "期望薪资" })}: {data.jobIntention.targetSalary}</div>
              )}
              {data.jobIntention.preferredLocation && (
                <div>{t("cvbuilder.preferredLocation", { default: "期望地点" })}: {data.jobIntention.preferredLocation}</div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className="text-gray-700 leading-relaxed mt-3">
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-8 pdf-section">
          <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
            {t("cvbuilder.personalProfile", { default: "个人简介" })}
          </h2>
          <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'} pdf-item`}>
            {data.profile.summary}
          </div>
        </section>
      )}

      {/* Two Column Layout */}
      <div className={`grid gap-8 ${isExport ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-2'}`}>
        {/* Left Column */}
        <div className="space-y-8">
          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-4">
                {(data.work || []).map((job: any, index: number) => (
                  <div key={job.id || index} className="border-l-2 border-gray-200 pl-4 pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.position}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.company}</p>
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                    </p>
                    {job.location && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.location}</p>
                    )}
                    {job.description && (
                      <p className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{job.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-4">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={edu.id || index} className="border-l-2 border-gray-200 pl-4 pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.school}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                    </p>
                    {edu.location && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</p>}
                    {edu.gpa && <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</p>}
                    {edu.description && (
                      <p className={`text-gray-700 mt-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-4">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="border-l-2 border-gray-200 pl-4 pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.name}</h3>
                    {project.url && (
                      <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                    )}
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                    </p>
                    <p className={`text-gray-700 mt-2 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <span key={techIndex} className={`px-2 py-1 bg-gray-100 text-gray-700 rounded ${isExport ? 'text-xs' : 'text-xs'}`}>
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.skills", { default: "专业技能" })}
              </h2>
              <div className="space-y-3">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="flex justify-between items-center pdf-item">
                    <span className={`text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{skill.name}</span>
                    {skill.level && (
                      <Badge className={`bg-gray-100 text-gray-600 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {skill.level}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-3">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="flex justify-between items-center pdf-item">
                    <span className={`text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.language}</span>
                    <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-3">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                    {award.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-3">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                    {cert.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-2 py-1 bg-gray-100 text-gray-700 rounded ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </section>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-4">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="border-l-2 border-gray-200 pl-4 pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.organization}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</p>
                    <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                    </p>
                    {vol.description && (
                      <p className={`text-gray-700 mt-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="space-y-4">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                    {ref.phone && (
                      <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-light text-gray-900 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-sm' : 'text-base'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-4">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="pdf-item">
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.title}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                    {pub.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}