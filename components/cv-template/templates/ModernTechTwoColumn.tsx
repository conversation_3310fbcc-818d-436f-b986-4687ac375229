"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ModernTechTwoColumnProps {
  data?: any;
  isExport?: boolean;
}

export default function ModernTechTwoColumn({
  data: propData,
  isExport = false
}: ModernTechTwoColumnProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateLocal = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === 'en' ? "en-US" : "zh-CN", { year: "numeric", month: "short" });
  };

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '20px' : '32px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#000',
    boxShadow: isExport ? 'none' : '0 4px 24px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.6',
    fontFamily: isExport ? 'PingFang SC, Microsoft YaHei, SimSun, sans-serif' : 'inherit',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <div className="mb-6 text-center">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-2"
            style={{ width: isExport ? 64 : 80, height: isExport ? 64 : 80, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-2 flex items-center justify-center bg-blue-200 text-blue-700 font-bold"
            style={{ width: isExport ? 64 : 80, height: isExport ? 64 : 80, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 28 : 36 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-bold text-gray-900 mb-2 ${isExport ? 'text-2xl' : 'text-3xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        <div className="text-gray-600 space-y-1">
          <p className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</p>
          <p className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</p>
          <p className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</p>
          {data.personal?.website && (
            <p className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</p>
          )}
          {data.personal?.linkedin && (
            <p className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</p>
          )}
          {data.personal?.github && (
            <p className={`text-blue-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</p>
          )}
        </div>
      </div>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention?.position && (
        <div className="mb-4">
          <h2 className={`font-semibold text-gray-800 mb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>{t("cvbuilder.jobIntention", { default: "求职意向" })}</h2>
          <p className={`text-gray-700 ${isExport ? 'text-base' : 'text-lg'}`}>{data.jobIntention.position}</p>
        </div>
      )}

      <div className={`grid gap-6 ${isExport ? 'grid-cols-[2fr_1fr]' : 'grid-cols-1 lg:grid-cols-[2fr_1fr]'}`}>
        {/* Left Column */}
        <div className="space-y-4">
          {/* Profile */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-2 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.personalProfile", { default: "个人简介" })}
              </h2>
              <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>{data.profile.summary}</p>
            </section>
          )}

          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-3">
                {(data.work || []).map((job: any, index: number) => (
                  <div key={index} className={`border-l-4 border-blue-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{job.position}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{job.company}</p>
                    <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{job.description}</p>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.projects", { default: "项目经验" })}
              </h2>
              <div className="space-y-3">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className={`border-l-4 border-green-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{project.name}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {project.startDate && project.endDate ? `${project.startDate} - ${project.endDate}` : ""}
                      </span>
                    </div>
                    {project.url && (
                      <p className={`text-blue-600 mb-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                    )}
                    <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} variant="secondary" className={`${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-3">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={index} className={`border-l-4 border-purple-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{edu.degree}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</p>
                    {edu.gpa && <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</p>}
                    {edu.description && (
                      <p className={`text-gray-700 mt-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-2">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className={`border-l-4 border-orange-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                    {pub.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{formatDateLocal(pub.date)}</p>
                    )}
                    {pub.url && (
                      <p className={`text-blue-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.url}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-3">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className={`border-l-4 border-teal-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <div className="flex justify-between items-start mb-1">
                      <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{vol.organization}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </span>
                    </div>
                    <p className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.role}</p>
                    <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="space-y-3">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className={`border-l-4 border-indigo-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-base' : 'text-lg'}`}>{ref.name}</h3>
                    <p className={`text-gray-600 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</p>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                    {ref.phone && (
                      <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.skills", { default: "技能" })}
              </h2>
              <div className="space-y-3">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className={`flex justify-between items-center ${isExport ? 'pdf-item' : ''}`}>
                    <span className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</span>
                    {skill.level && (
                      <Badge variant="outline" className={`${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {skill.level}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-2">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className={`flex justify-between items-center ${isExport ? 'pdf-item' : ''}`}>
                    <span className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                    <span className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-2">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className={`border-l-4 border-yellow-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                    {cert.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{formatDateLocal(cert.date)}</p>
                    )}
                    {cert.url && (
                      <p className={`text-blue-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.url}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.awards", { default: "获奖情况" })}
              </h2>
              <div className="space-y-2">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className={`border-l-4 border-red-500 pl-3 ${isExport ? 'pdf-item' : ''}`}>
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                    {award.date && (
                      <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{formatDateLocal(award.date)}</p>
                    )}
                    {award.description && (
                      <p className={`text-gray-700 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <section className="pdf-section">
              <h2 className={`font-semibold text-gray-800 mb-3 border-b-2 border-blue-500 pb-1 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-1">
                {(data.interests || []).map((interest: any, index: number) => (
                  <Badge key={interest.id || index} variant="secondary" className={`${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                    {interest.name || interest}
                  </Badge>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
} 