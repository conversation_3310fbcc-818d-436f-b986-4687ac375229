"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface MinimalAcademicTimelineProps {
  data?: any;
  isExport?: boolean;
}

export default function MinimalAcademicTimeline({
  data: propData,
  isExport = false
}: MinimalAcademicTimelineProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 极简学术时间线风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    padding: isExport ? '30px 40px 30px 40px' : '40px 0 0 0',
    margin: isExport ? '0 auto' : '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1f2937',
    boxShadow: isExport ? 'none' : '0 1px 3px rgba(0,0,0,0.02)',
    borderRadius: isExport ? '0' : '0',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Georgia, serif',
    display: isExport ? 'block' : undefined,
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="text-center mb-12">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-6"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-6 flex items-center justify-center bg-gray-100 text-gray-500 font-light"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-light text-gray-800 mb-3 tracking-wide ${isExport ? 'text-3xl' : 'text-4xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <div className={`text-gray-600 mb-4 font-light ${isExport ? 'text-base' : 'text-lg'}`}>
            {data.jobIntention.position}
          </div>
        )}
        <div className="flex justify-center space-x-8 text-gray-500">
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
        </div>
        {(data.personal?.website || data.personal?.linkedin || data.personal?.github) && (
          <div className="flex justify-center space-x-6 mt-3 text-gray-500">
            {data.personal?.website && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
            )}
            {data.personal?.linkedin && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
            )}
            {data.personal?.github && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
            )}
          </div>
        )}
      </header>

      {/* Personal Information - Only for PDF Export */}
      {isExport && (
        <section className="pdf-section mt-0 mb-2 pt-0 pb-0">
          <h2 className="font-light text-gray-800 text-center text-lg mb-3">
            {t("cvbuilder.personalInfo", { default: "个人信息" })}
          </h2>
          <div className="max-w-2xl mx-auto space-y-1">
            <div className="grid gap-2 pdf-item grid-cols-1 text-sm">
              <div className="text-center">
                <span className="font-medium text-gray-600">📧 邮箱：</span>
                <span className="text-gray-800">{data.personal?.email || "<EMAIL>"}</span>
              </div>
              <div className="text-center">
                <span className="font-medium text-gray-600">📱 电话：</span>
                <span className="text-gray-800">{data.personal?.phone || "+86 138 0000 0000"}</span>
              </div>
              <div className="text-center">
                <span className="font-medium text-gray-600">📍 地址：</span>
                <span className="text-gray-800">{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
              </div>
              {data.personal?.website && (
                <div className="text-center">
                  <span className="font-medium text-gray-600">🌐 网站：</span>
                  <span className="text-gray-800">{data.personal.website}</span>
                </div>
              )}
              {data.personal?.linkedin && (
                <div className="text-center">
                  <span className="font-medium text-gray-600">💼 LinkedIn：</span>
                  <span className="text-gray-800">{data.personal.linkedin}</span>
                </div>
              )}
              {data.personal?.github && (
                <div className="text-center">
                  <span className="font-medium text-gray-600">💻 GitHub：</span>
                  <span className="text-gray-800">{data.personal.github}</span>
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Profile */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-6' : 'text-2xl mb-6'}`}>
            {t("cvbuilder.personalProfile", { default: "研究概述" })}
          </h2>
          <div className={`text-gray-700 leading-relaxed text-justify max-w-3xl mx-auto pdf-item ${isExport ? 'text-xs' : 'text-base'}`}>
            {data.profile.summary}
          </div>
        </section>
      )}

      {/* Work Experience Timeline */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-6' : 'text-2xl mb-6'}`}>
            {t("cvbuilder.workExperience", { default: "学术经历" })}
          </h2>
          <div className="relative">
            {/* 时间轴线 */}
            <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300"></div>

            {(data.work || []).map((work: any, index: number) => (
              <div
                key={work.id || index}
                className={`relative flex ${isExport ? 'mb-2' : 'mb-6'} ${index % 2 === 0 ? 'justify-start' : 'justify-end'} pdf-item`}
              >
                {/* 时间轴节点 */}
                <div className={`absolute left-1/2 transform -translate-x-1/2 ${isExport ? 'w-2 h-2 border-2' : 'w-4 h-4 border-4'} bg-gray-600 rounded-full border-white shadow`}></div>

                {/* 内容卡片 */}
                <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className={`bg-gray-50 rounded-lg shadow-sm ${isExport ? 'p-2' : 'p-4'}`}>
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs mb-0.5' : 'text-base mb-1'}`}>{work.position}</h3>
                    <div className={`text-gray-600 ${isExport ? 'text-xs mb-0.5' : 'text-sm mb-1'}`}>{work.company}</div>
                    {work.location && <div className={`text-gray-500 ${isExport ? 'text-xs mb-0.5' : 'text-sm mb-1'}`}>{work.location}</div>}
                    <div className={`text-gray-500 ${isExport ? 'text-xs mb-1' : 'text-sm mb-2'}`}>
                      {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                    </div>
                    {work.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {work.description}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Education Timeline */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-6' : 'text-2xl mb-6'}`}>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-2' : 'space-y-4'}`}>
            {(data.education || []).map((edu: any, index: number) => (
              <div key={edu.id || index} className={`text-center border-b border-gray-200 pdf-item ${isExport ? 'pb-2' : 'pb-4'}`}>
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{edu.school}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                {edu.location && <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</div>}
                {edu.gpa && <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</div>}
                <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                </div>
                {edu.description && (
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs mt-1' : 'text-sm mt-2'}`}>
                    {edu.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Projects Timeline */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-6' : 'text-2xl mb-6'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="relative">
            {/* 时间轴线 */}
            <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300"></div>

            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className={`relative flex items-center ${isExport ? 'mb-2' : 'mb-6'} ${index % 2 === 0 ? 'justify-start' : 'justify-end'} pdf-item`}>
                {/* 时间轴节点 */}
                <div className={`absolute left-1/2 transform -translate-x-1/2 ${isExport ? 'w-2 h-2 border-2' : 'w-4 h-4 border-4'} bg-gray-600 rounded-full border-white shadow`}></div>

                {/* 内容卡片 */}
                <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className={`bg-gray-50 rounded-lg shadow-sm ${isExport ? 'p-2' : 'p-4'}`}>
                    <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs mb-0.5' : 'text-base mb-1'}`}>{project.name}</h3>
                    {project.url && <div className={`text-gray-600 ${isExport ? 'text-xs mb-0.5' : 'text-sm mb-1'}`}>{project.url}</div>}
                    {project.startDate && project.endDate && (
                      <div className={`text-gray-500 ${isExport ? 'text-xs mb-1' : 'text-sm mb-2'}`}>
                        {project.startDate} - {project.endDate}
                      </div>
                    )}
                    <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {project.description}
                    </div>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className={`flex flex-wrap gap-1 ${isExport ? 'mt-1' : 'mt-3'}`}>
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} className={`bg-gray-100 text-gray-700 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.skills", { default: "研究技能" })}
          </h2>
          <div className={`flex flex-wrap justify-center max-w-2xl mx-auto ${isExport ? 'gap-1' : 'gap-3'}`}>
            {(data.skills || []).map((skill: any, index: number) => (
              <div key={skill.id || index} className={`bg-gray-100 rounded-full pdf-item ${isExport ? 'px-2 py-0.5' : 'px-4 py-2'}`}>
                <span className={`text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{skill.name}</span>
                {skill.level && (
                  <span className={`text-gray-600 ml-1 ${isExport ? 'text-xs' : 'text-sm'}`}>({skill.level})</span>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className={`flex flex-wrap justify-center max-w-2xl mx-auto ${isExport ? 'gap-1' : 'gap-3'}`}>
            {(data.languages || []).map((lang: any, index: number) => (
              <div key={index} className={`bg-gray-100 rounded-full pdf-item ${isExport ? 'px-2 py-0.5' : 'px-4 py-2'}`}>
                <span className={`text-gray-800 ${isExport ? 'text-xs' : 'text-sm'}`}>{lang.language}</span>
                <span className={`text-gray-600 ml-1 ${isExport ? 'text-xs' : 'text-sm'}`}>({lang.proficiency})</span>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-1' : 'space-y-4'}`}>
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className={`text-center border-b border-gray-200 pdf-item ${isExport ? 'pb-1' : 'pb-4'}`}>
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{pub.title}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</div>
                {pub.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-1' : 'space-y-4'}`}>
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className={`text-center border-b border-gray-200 pdf-item ${isExport ? 'pb-1' : 'pb-4'}`}>
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{award.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</div>
                {award.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-1' : 'space-y-4'}`}>
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className={`text-center border-b border-gray-200 pdf-item ${isExport ? 'pb-1' : 'pb-4'}`}>
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{cert.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</div>
                {cert.date && (
                  <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className={`flex flex-wrap justify-center max-w-2xl mx-auto ${isExport ? 'gap-1' : 'gap-3'}`}>
            {(data.interests || []).map((interest: any, index: number) => (
              <span key={interest.id || index} className={`bg-gray-100 text-gray-700 rounded-lg ${isExport ? 'px-1 py-0.5 text-xs' : 'px-3 py-2 text-sm'}`}>
                {interest.name || interest}
              </span>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-1' : 'space-y-4'}`}>
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className={`text-center border-b border-gray-200 pdf-item ${isExport ? 'pb-1' : 'pb-4'}`}>
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{vol.organization}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</div>
                <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                </div>
                {vol.description && (
                  <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs mt-1' : 'text-sm mt-2'}`}>
                    {vol.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className={`pdf-section ${isExport ? 'mb-8' : 'mb-2'} mt-0 pt-0 pb-0`}>
          <h2 className={`font-light text-gray-800 text-center ${isExport ? 'text-lg mb-4' : 'text-2xl mb-4'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className={`max-w-2xl mx-auto ${isExport ? 'space-y-1' : 'grid grid-cols-1 md:grid-cols-2 gap-6'}`}>
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="text-center pdf-item">
                <h3 className={`font-semibold text-gray-800 ${isExport ? 'text-xs' : 'text-base'}`}>{ref.name}</h3>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</div>
                <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</div>
                {ref.phone && (
                  <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}
