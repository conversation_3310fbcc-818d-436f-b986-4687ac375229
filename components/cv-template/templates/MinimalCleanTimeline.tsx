"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface MinimalCleanTimelineProps {
  data?: any;
  isExport?: boolean;
}

export default function MinimalCleanTimeline({
  data: propData,
  isExport = false
}: MinimalCleanTimelineProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 极简清洁时间线风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '30px' : '40px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1f2937',
    boxShadow: isExport ? 'none' : '0 1px 3px rgba(0,0,0,0.02)',
    borderRadius: isExport ? '0' : '0',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };
  return (
    <div className="pdf-container pdf-optimized" style={containerStyle}>
      {/* Header Section */}
      <div className="pdf-section pdf-item text-center mb-8">
        {data.personal?.avatar && (
          <div className="flex justify-center mb-4">
            <img
              src={data.personal.avatar}
              alt="Profile"
              className={`rounded-full object-cover ${isExport ? 'w-16 h-16' : 'w-20 h-20'}`}
            />
          </div>
        )}
        <h1 className={`font-light text-gray-900 mb-2 ${isExport ? 'text-2xl' : 'text-3xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <p className={`text-gray-600 mb-3 ${isExport ? 'text-base' : 'text-lg'}`}>{data.jobIntention.position}</p>
        )}
        <div className={`flex justify-center flex-wrap gap-4 text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
          <span>{data.personal?.email || "<EMAIL>"}</span>
          <span>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
          {data.personal?.website && (
            <span className="text-blue-600">{data.personal.website}</span>
          )}
          {data.personal?.linkedin && (
            <span className="text-blue-600">LinkedIn: {data.personal.linkedin}</span>
          )}
          {data.personal?.github && (
            <span className="text-blue-600">GitHub: {data.personal.github}</span>
          )}
        </div>
      </div>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.jobIntention", { default: "求职意向" })}
          </h2>
          <div className="pdf-item bg-gray-50 p-4 rounded-lg">
            {data.jobIntention.position && (
              <div className={`font-medium text-gray-800 mb-2 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.targetSalary", { default: "Target Salary" })}: {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.preferredLocation", { default: "Preferred Location" })}: {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile Section */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.personalProfile", { default: "个人简介" })}
          </h2>
          <p className={`text-gray-700 leading-relaxed pdf-item ${isExport ? 'text-sm' : 'text-base'}`}>
            {data.profile.summary}
          </p>
        </section>
      )}

      {/* Experience Timeline */}
      {sectionsVisible.work !== false && (data.work || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-6 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.workExperience", { default: "工作经历" })}
          </h2>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-px bg-gray-300"></div>

            {(data.work || []).map((work: any, index: number) => (
              <div key={work.id || index} className={`relative pl-10 pdf-item ${index === (data.work || []).length - 1 ? '' : 'pb-8'}`}>
                {/* Timeline dot */}
                <div className={`absolute left-2 top-1 bg-gray-400 rounded-full border-2 border-white ${isExport ? 'w-2 h-2' : 'w-3 h-3'}`}></div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className={`font-medium text-gray-900 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{work.position}</h3>
                  <p className={`text-gray-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{work.company}</p>
                  {work.location && (
                    <p className={`text-gray-500 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{work.location}</p>
                  )}
                  <p className={`text-gray-500 mb-3 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                  </p>
                  {work.description && (
                    <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{work.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Projects Timeline */}
      {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-6 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.projects", { default: "项目经验" })}
          </h2>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-px bg-gray-300"></div>

            {(data.projects || []).map((project: any, index: number) => (
              <div key={index} className={`relative pl-10 pdf-item ${index === (data.projects || []).length - 1 ? '' : 'pb-8'}`}>
                {/* Timeline dot */}
                <div className={`absolute left-2 top-1 bg-gray-400 rounded-full border-2 border-white ${isExport ? 'w-2 h-2' : 'w-3 h-3'}`}></div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className={`font-medium text-gray-900 mb-1 ${isExport ? 'text-sm' : 'text-base'}`}>{project.name}</h3>
                  {project.url && (
                    <p className={`text-blue-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</p>
                  )}
                  {project.startDate && project.endDate && (
                    <p className={`text-gray-500 mb-3 ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {project.startDate} - {project.endDate}
                    </p>
                  )}
                  {project.description && (
                    <p className={`text-gray-700 leading-relaxed mb-3 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.description}</p>
                  )}
                  {project.technologies && project.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech: string, techIndex: number) => (
                        <Badge key={techIndex} className={`bg-gray-100 text-gray-700 hover:bg-gray-200 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Education */}
      {sectionsVisible.education !== false && (data.education || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.education", { default: "教育背景" })}
          </h2>
          {(data.education || []).map((edu: any, index: number) => (
            <div key={edu.id || index} className="mb-4 pdf-item">
              <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
              <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
              {edu.location && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.location}</p>}
              {edu.gpa && <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>GPA: {edu.gpa}</p>}
              <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
              </p>
              {edu.description && (
                <p className={`text-gray-700 leading-relaxed mt-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.description}</p>
              )}
            </div>
          ))}
        </section>
      )}

      {/* Skills */}
      {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.skills", { default: "专业技能" })}
          </h2>
          <div className="flex flex-wrap gap-2">
            {(data.skills || []).map((skill: any, index: number) => (
              <Badge key={skill.id || index} className={`bg-gray-100 text-gray-700 hover:bg-gray-200 pdf-item ${isExport ? 'text-xs px-2 py-1' : 'text-sm'}`}>
                {skill.name}
                {skill.level && ` (${skill.level})`}
              </Badge>
            ))}
          </div>
        </section>
      )}

      {/* Languages */}
      {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.languages", { default: "语言能力" })}
          </h2>
          <div className="flex flex-wrap gap-2">
            {(data.languages || []).map((lang: any, index: number) => (
              <Badge key={index} className={`bg-gray-100 text-gray-700 hover:bg-gray-200 pdf-item ${isExport ? 'text-xs px-2 py-1' : 'text-sm'}`}>
                {lang.language} ({lang.proficiency})
              </Badge>
            ))}
          </div>
        </section>
      )}

      {/* Certifications */}
      {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.certifications", { default: "证书" })}
          </h2>
          <div className="space-y-3">
            {(data.certifications || []).map((cert: any, index: number) => (
              <div key={index} className="pdf-item bg-gray-50 p-3 rounded">
                <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</p>
                {cert.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Awards */}
      {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.awards", { default: "获奖荣誉" })}
          </h2>
          <div className="space-y-3">
            {(data.awards || []).map((award: any, index: number) => (
              <div key={index} className="pdf-item bg-gray-50 p-3 rounded">
                <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</p>
                {award.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Interests */}
      {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.interests", { default: "兴趣爱好" })}
          </h2>
          <div className="flex flex-wrap gap-2">
            {(data.interests || []).map((interest: any, index: number) => (
              <Badge key={interest.id || index} className={`bg-gray-100 text-gray-700 hover:bg-gray-200 pdf-item ${isExport ? 'text-xs px-2 py-1' : 'text-sm'}`}>
                {interest.name || interest}
              </Badge>
            ))}
          </div>
        </section>
      )}

      {/* Volunteer Experience */}
      {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
          </h2>
          <div className="space-y-4">
            {(data.volunteer || []).map((vol: any, index: number) => (
              <div key={index} className="pdf-item bg-gray-50 p-4 rounded">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                    <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</p>
                  </div>
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                  </p>
                </div>
                {vol.description && (
                  <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.description}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Publications */}
      {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
        <section className="pdf-section mb-8">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.publications", { default: "发表作品" })}
          </h2>
          <div className="space-y-3">
            {(data.publications || []).map((pub: any, index: number) => (
              <div key={index} className="pdf-item bg-gray-50 p-3 rounded">
                <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</p>
                {pub.date && (
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* References */}
      {sectionsVisible.references !== false && (data.references || []).length > 0 && (
        <section className="pdf-section">
          <h2 className={`font-light text-gray-800 mb-4 border-b border-gray-200 pb-2 ${isExport ? 'text-lg' : 'text-xl'}`}>
            {t("cvbuilder.references", { default: "推荐人" })}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(data.references || []).map((ref: any, index: number) => (
              <div key={index} className="pdf-item bg-gray-50 p-4 rounded">
                <h3 className={`font-medium text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</p>
                <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</p>
                {ref.phone && (
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.phone}</p>
                )}
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}