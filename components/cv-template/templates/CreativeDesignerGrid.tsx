"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface CreativeDesignerGridProps {
  data?: any;
  isExport?: boolean;
}

export default function CreativeDesignerGrid({
  data: propData,
  isExport = false
}: CreativeDesignerGridProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 创意设计师网格风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '30px' : '40px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1f2937',
    boxShadow: isExport ? 'none' : '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: isExport ? '0' : '8px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.5' : '1.6',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* Header */}
      <header className="text-center mb-10">
        {data.personal?.avatar && (
          <img
            src={data.personal.avatar}
            alt={data.personal.name || 'Avatar'}
            className="pdf-avatar mx-auto mb-6"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'block' }}
          />
        )}
        {!data.personal?.avatar && (
          <div
            className="pdf-avatar mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-purple-100 to-pink-100 text-purple-600 font-light"
            style={{ width: isExport ? 80 : 100, height: isExport ? 80 : 100, borderRadius: '50%', objectFit: 'cover', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: isExport ? 32 : 40 }}
          >
            {data.personal?.name?.[0]?.toUpperCase() || '?'}
          </div>
        )}
        <h1 className={`font-bold text-gray-900 mb-3 tracking-wide ${isExport ? 'text-3xl' : 'text-4xl'}`}>
          {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
        </h1>
        {data.jobIntention?.position && (
          <div className={`text-gray-700 mb-4 font-medium ${isExport ? 'text-lg' : 'text-xl'}`}>
            {data.jobIntention.position}
          </div>
        )}
        <div className="flex justify-center space-x-6 text-gray-600">
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.email || "<EMAIL>"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.phone || "+86 138 0000 0000"}</span>
          <span className={isExport ? 'text-sm' : 'text-base'}>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
        </div>
        {(data.personal?.website || data.personal?.linkedin || data.personal?.github) && (
          <div className="flex justify-center space-x-6 mt-3 text-gray-600">
            {data.personal?.website && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{data.personal.website}</span>
            )}
            {data.personal?.linkedin && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>LinkedIn: {data.personal.linkedin}</span>
            )}
            {data.personal?.github && (
              <span className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>GitHub: {data.personal.github}</span>
            )}
          </div>
        )}
      </header>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-8 pdf-section">
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-6 pdf-item">
            <h2 className={`font-bold text-purple-700 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
              {t("cvbuilder.jobIntention", { default: "求职意向" })}
            </h2>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.targetSalary", { default: "Target Salary" })}:</span> {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  <span className="font-medium">{t("cvbuilder.preferredLocation", { default: "Preferred Location" })}:</span> {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Main Grid Layout */}
      <div className={`grid gap-8 ${isExport ? 'grid-cols-3' : 'grid-cols-1 lg:grid-cols-3'}`}>
        {/* Left Column */}
        <div className={`space-y-6 ${isExport ? 'col-span-1' : 'lg:col-span-1'}`}>
          {/* Profile */}
          {sectionsVisible.profile !== false && data.profile?.summary && (
            <section className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-purple-700 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.personalProfile", { default: "创意理念" })}
              </h2>
              <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'} pdf-item`}>
                {data.profile.summary}
              </div>
            </section>
          )}

          {/* Skills */}
          {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
            <section className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-blue-700 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.skills", { default: "技能专长" })}
              </h2>
              <div className="space-y-3">
                {(data.skills || []).map((skill: any, index: number) => (
                  <div key={skill.id || index} className="bg-white/70 rounded-lg p-3 pdf-item">
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</div>
                    {skill.level && (
                      <Badge className={`bg-blue-100 text-blue-800 mt-1 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                        {skill.level}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages */}
          {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
            <section className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-green-700 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.languages", { default: "语言能力" })}
              </h2>
              <div className="space-y-3">
                {(data.languages || []).map((lang: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-3 pdf-item">
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</div>
                    <div className={`text-green-600 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Interests */}
          {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
            <section className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-yellow-700 mb-4 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.interests", { default: "兴趣爱好" })}
              </h2>
              <div className="flex flex-wrap gap-2">
                {(data.interests || []).map((interest: any, index: number) => (
                  <span key={interest.id || index} className={`px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {interest.name || interest}
                  </span>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column */}
        <div className={`space-y-6 ${isExport ? 'col-span-2' : 'lg:col-span-2'}`}>
          {/* Work Experience */}
          {sectionsVisible.work !== false && (data.work || []).length > 0 && (
            <section className="bg-gradient-to-br from-pink-50 to-rose-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-pink-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.workExperience", { default: "工作经历" })}
              </h2>
              <div className="space-y-6">
                {(data.work || []).map((work: any, index: number) => (
                  <div key={work.id || index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-base' : 'text-lg'}`}>{work.position}</h3>
                        <div className={`text-pink-600 font-medium ${isExport ? 'text-sm' : 'text-base'}`}>{work.company}</div>
                        {work.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{work.location}</div>}
                      </div>
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                      </div>
                    </div>
                    {work.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {work.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects */}
          {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
            <section className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-indigo-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.projects", { default: "项目作品" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.projects || []).map((project: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <h3 className={`font-bold text-gray-800 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{project.name}</h3>
                    {project.url && <div className={`text-indigo-600 mb-2 ${isExport ? 'text-xs' : 'text-sm'}`}>{project.url}</div>}
                    <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-xs' : 'text-sm'}`}>
                      {project.description}
                    </div>
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {project.technologies.map((tech: string, techIndex: number) => (
                          <Badge key={techIndex} className={`bg-indigo-100 text-indigo-800 ${isExport ? 'text-xs px-1 py-0' : 'text-xs'}`}>
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Education */}
          {sectionsVisible.education !== false && (data.education || []).length > 0 && (
            <section className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-teal-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.education", { default: "教育背景" })}
              </h2>
              <div className="space-y-4">
                {(data.education || []).map((edu: any, index: number) => (
                  <div key={edu.id || index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
                        <div className={`text-teal-600 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</div>
                        {edu.location && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.location}</div>}
                        {edu.gpa && <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>GPA: {edu.gpa}</div>}
                      </div>
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                      </div>
                    </div>
                    {edu.description && (
                      <div className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-base'}`}>
                        {edu.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
            <section className="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-amber-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.awards", { default: "获奖荣誉" })}
              </h2>
              <div className="space-y-4">
                {(data.awards || []).map((award: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</h3>
                        <div className={`text-amber-600 ${isExport ? 'text-sm' : 'text-base'}`}>{award.issuer}</div>
                      </div>
                      {award.date && (
                        <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{award.date}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
            <section className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-emerald-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.certifications", { default: "证书" })}
              </h2>
              <div className="space-y-4">
                {(data.certifications || []).map((cert: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</h3>
                    <div className={`text-emerald-600 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.issuer}</div>
                    {cert.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Volunteer Experience */}
          {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
            <section className="bg-gradient-to-br from-violet-50 to-purple-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-violet-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
              </h2>
              <div className="space-y-4">
                {(data.volunteer || []).map((vol: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</h3>
                      <span className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>
                        {formatDateRangeLocal(vol.startDateYear, vol.startDateMonth, vol.endDateYear, vol.endDateMonth)}
                      </span>
                    </div>
                    <div className={`text-violet-600 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.position}</div>
                    {vol.description && (
                      <div className={`text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.description}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Publications */}
          {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
            <section className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-slate-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.publications", { default: "发表作品" })}
              </h2>
              <div className="space-y-4">
                {(data.publications || []).map((pub: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</h3>
                    <div className={`text-slate-600 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.publication}</div>
                    {pub.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.date}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* References */}
          {sectionsVisible.references !== false && (data.references || []).length > 0 && (
            <section className="bg-gradient-to-br from-rose-50 to-pink-50 rounded-lg p-6 pdf-section">
              <h2 className={`font-bold text-rose-700 mb-6 ${isExport ? 'text-lg' : 'text-xl'}`}>
                {t("cvbuilder.references", { default: "推荐人" })}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.references || []).map((ref: any, index: number) => (
                  <div key={index} className="bg-white/70 rounded-lg p-4 pdf-item">
                    <h3 className={`font-bold text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</h3>
                    <div className={`text-rose-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.title} at {ref.company}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.email}</div>
                    {ref.phone && (
                      <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.phone}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}