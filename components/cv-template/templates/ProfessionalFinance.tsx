"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface ProfessionalFinanceProps {
  data?: any;
  isExport?: boolean;
}

export default function ProfessionalFinance({
  data: propData,
  isExport = false
}: ProfessionalFinanceProps = {}) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  // PDF导出样式调整 - 专业金融风格
  const containerStyle = {
    width: isExport ? '794px' : '100%',
    maxWidth: isExport ? '794px' : '100%',
    minHeight: isExport ? 'auto' : '100%',
    padding: isExport ? '30px' : '40px',
    margin: '0 auto',
    boxSizing: 'border-box' as const,
    background: 'white',
    color: '#1a1a1a',
    boxShadow: isExport ? 'none' : '0 4px 16px rgba(0,0,0,0.06)',
    borderRadius: isExport ? '0' : '4px',
    fontSize: isExport ? '12px' : '14px',
    lineHeight: isExport ? '1.4' : '1.5',
    fontFamily: 'Times New Roman, serif',
  };

  return (
    <div className="pdf-container bg-white text-gray-900 mx-auto" style={containerStyle}>
      {/* 头部信息 */}
      <header className="text-center mb-8 pdf-section border-b-2 border-gray-800 pb-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{data.personal.name || "姓名"}</h1>
        <div className="text-xl text-gray-700 mb-4 font-semibold">{data.jobIntention.position || "金融分析师"}</div>
        <div className="flex justify-center space-x-8 text-sm text-gray-600">
          <span>{data.personal.email || "<EMAIL>"}</span>
          <span>{data.personal.phone || "电话号码"}</span>
          <span>{data.personal.address || "地址"}</span>
        </div>
      </header>

      {/* 专业概述 */}
      {data.profile.summary && (
        <section className="mb-8 pdf-section">
          <h2 className="text-xl font-bold text-gray-800 mb-4 border-b border-gray-400 pb-2">专业概述</h2>
          <div className="text-sm text-gray-700 leading-relaxed text-justify whitespace-pre-line pdf-item">
            {data.profile.summary}
          </div>
        </section>
      )}

      {/* 核心技能 */}
      {data.skills.length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className="text-xl font-bold text-gray-800 mb-4 border-b border-gray-400 pb-2">核心技能</h2>
          <div className="grid grid-cols-3 gap-4">
            {data.skills.map((skill: any) => (
              <div key={skill.id} className="pdf-item">
                <div className="font-semibold text-gray-800 mb-1">{skill.name}</div>
                {skill.level && (
                  <div className="text-sm text-gray-600">{skill.level}</div>
                )}
              </div>
            ))}
          </div>
        </section>
      )}

      {/* 工作经历 */}
      {data.work.length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className="text-xl font-bold text-gray-800 mb-4 border-b border-gray-400 pb-2">工作经历</h2>
          {data.work.map((work: any) => (
            <div key={work.id} className="mb-6 pdf-item">
              <div className="flex justify-between items-start mb-2">
                <div style={{ flex: '1' }}>
                  <h3 className="font-bold text-gray-800 text-lg">{work.position}</h3>
                  <div className="text-gray-600 font-semibold">{work.company}</div>
                  {work.location && (
                    <div className="text-sm text-gray-500">{work.location}</div>
                  )}
                </div>
                <div className="text-sm text-gray-500 text-right" style={{ minWidth: '120px' }}>
                  {formatDateRangeLocal(work.startDateYear, work.startDateMonth, work.endDateYear, work.endDateMonth)}
                </div>
              </div>
              {work.description && (
                <div className="text-sm text-gray-700 leading-relaxed text-justify whitespace-pre-line pl-4 border-l-2 border-gray-300">
                  {work.description}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* 教育背景 */}
      {data.education.length > 0 && (
        <section className="mb-8 pdf-section">
          <h2 className="text-xl font-bold text-gray-800 mb-4 border-b border-gray-400 pb-2">教育背景</h2>
          {data.education.map((edu: any) => (
            <div key={edu.id} className="mb-4 pdf-item">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-bold text-gray-800">{edu.school}</h3>
                  <div className="text-gray-600">{edu.degree} {edu.field && `- ${edu.field}`}</div>
                  {edu.location && <div className="text-sm text-gray-500">{edu.location}</div>}
                </div>
                <div className="text-sm text-gray-500">
                  {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                </div>
              </div>
              {edu.description && (
                <div className="text-sm text-gray-700 mt-2 leading-relaxed text-justify whitespace-pre-line pl-4 border-l-2 border-gray-300">
                  {edu.description}
                </div>
              )}
            </div>
          ))}
        </section>
      )}

      {/* 专业认证 */}
      <section className="mb-8 pdf-section">
        <h2 className="text-xl font-bold text-gray-800 mb-4 border-b border-gray-400 pb-2">专业认证</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="pdf-item">
            <div className="font-semibold text-gray-800">CFA (特许金融分析师)</div>
            <div className="text-sm text-gray-600">CFA Institute</div>
          </div>
          <div className="pdf-item">
            <div className="font-semibold text-gray-800">FRM (金融风险管理师)</div>
            <div className="text-sm text-gray-600">GARP</div>
          </div>
        </div>
      </section>
    </div>
  );
} 