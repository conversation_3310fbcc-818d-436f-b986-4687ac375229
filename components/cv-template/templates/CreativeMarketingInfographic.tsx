"use client";
import { useCVBuilder } from "@/contexts/cv-builder";
import { useTranslations, useLocale } from "next-intl";
import { Badge } from '@/components/ui/badge';
import { formatDateRange, formatDate } from "@/lib/dateFormat";

interface CreativeMarketingInfographicProps {
  data?: any;
  isExport?: boolean;
}

export default function CreativeMarketingInfographic({
  data: propData,
  isExport = false
}: CreativeMarketingInfographicProps) {
  const contextData = useCVBuilder();
  const data = propData || contextData.data;
  const sectionsVisible = contextData.sectionsVisible || {};
  const t = useTranslations();
  const locale = useLocale();

  const formatDateRangeLocal = (startYear?: string, startMonth?: string, endYear?: string, endMonth?: string) => {
    return formatDateRange(startYear, startMonth, endYear, endMonth, locale, t("cvbuilder.present", { default: locale === 'en' ? "Present" : "至今" }));
  };

  const containerStyle = isExport ? {
    fontSize: '12px',
    lineHeight: '1.4',
    padding: '20px',
    maxWidth: '210mm',
    minHeight: '297mm',
    fontFamily: 'PingFang SC, Microsoft YaHei, SimSun, sans-serif'
  } : {};

  return (
    <div className="pdf-container bg-white text-gray-800 mx-auto" style={containerStyle}>
      {/* Header Section with Marketing Theme */}
      <div className="pdf-section pdf-item bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white p-6 mb-6 rounded-xl">
        <div className="text-center">
          {data.personal?.avatar && (
            <img
              src={data.personal.avatar}
              alt={data.personal.name || 'Avatar'}
              className={`mx-auto rounded-full object-cover mb-4 border-4 border-white ${isExport ? 'w-16 h-16' : 'w-20 h-20'}`}
            />
          )}
          <h1 className={`font-bold mb-3 ${isExport ? 'text-2xl' : 'text-4xl'}`}>
            {data.personal?.name || t("cvbuilder.yourName", { default: "您的姓名" })}
          </h1>
          {data.jobIntention?.position && (
            <p className={`mb-4 opacity-90 ${isExport ? 'text-lg' : 'text-2xl'}`}>{data.jobIntention.position}</p>
          )}
          <div className={`flex justify-center flex-wrap gap-4 opacity-80 ${isExport ? 'text-sm' : 'text-base'}`}>
            <span>{data.personal?.email || "<EMAIL>"}</span>
            <span>{data.personal?.phone || "+86 138 0000 0000"}</span>
            <span>{data.personal?.address || t("cvbuilder.beijingChina", { default: "北京，中国" })}</span>
            {data.personal?.website && <span>{data.personal.website}</span>}
            {data.personal?.linkedin && <span>LinkedIn: {data.personal.linkedin}</span>}
            {data.personal?.github && <span>GitHub: {data.personal.github}</span>}
          </div>
        </div>
      </div>

      {/* Job Intention */}
      {sectionsVisible.jobIntention !== false && data.jobIntention && (
        <section className="mb-6 pdf-section">
          <div className="pdf-item p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 flex items-center ${isExport ? 'text-lg' : 'text-2xl'}`}>
              <span className="w-3 h-3 bg-blue-600 rounded-full mr-3"></span>
              {t("cvbuilder.jobIntention", { default: "求职意向" })}
            </h2>
            {data.jobIntention.position && (
              <div className={`font-semibold text-gray-800 mb-3 ${isExport ? 'text-base' : 'text-lg'}`}>
                {t("cvbuilder.expectedPosition", { default: "Expected Position" })}: {data.jobIntention.position}
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {data.jobIntention.targetSalary && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.targetSalary", { default: "Target Salary" })}: {data.jobIntention.targetSalary}
                </div>
              )}
              {data.jobIntention.preferredLocation && (
                <div className={`text-gray-600 ${isExport ? 'text-sm' : 'text-base'}`}>
                  {t("cvbuilder.preferredLocation", { default: "Preferred Location" })}: {data.jobIntention.preferredLocation}
                </div>
              )}
            </div>
            {data.jobIntention.description && (
              <div className={`text-gray-700 leading-relaxed mt-3 ${isExport ? 'text-sm' : 'text-base'}`}>
                {data.jobIntention.description}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Profile Section */}
      {sectionsVisible.profile !== false && data.profile?.summary && (
        <section className="mb-6 pdf-section">
          <div className="pdf-item p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 flex items-center ${isExport ? 'text-lg' : 'text-2xl'}`}>
              <span className="w-3 h-3 bg-blue-600 rounded-full mr-3"></span>
              {t("cvbuilder.personalProfile", { default: "个人简介" })}
            </h2>
            <p className={`text-gray-700 leading-relaxed ${isExport ? 'text-sm' : 'text-lg'}`}>{data.profile.summary}</p>
          </div>
        </section>
      )}

      {/* Infographic Grid Layout */}
      <div className={`grid gap-6 mb-6 ${isExport ? 'grid-cols-3' : 'grid-cols-1 md:grid-cols-3'}`}>
        {/* Skills Infographic */}
        {sectionsVisible.skills !== false && (data.skills || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-white rounded-xl shadow-lg border-l-4 border-blue-600">
            <h2 className={`font-bold text-gray-900 mb-4 text-center ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.skills", { default: "专业技能" })}
            </h2>
            <div className="space-y-3">
              {(data.skills || []).slice(0, 5).map((skill: any, index: number) => (
                <div key={skill.id || index} className="text-center">
                  <div className={`font-medium text-gray-700 mb-2 ${isExport ? 'text-sm' : 'text-base'}`}>{skill.name}</div>
                  {skill.level && (
                    <div className="relative">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(parseInt(skill.level) / 5) * 100}%` }}
                        ></div>
                      </div>
                      <div className={`text-gray-500 mt-1 ${isExport ? 'text-xs' : 'text-sm'}`}>{skill.level}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Work Experience Timeline */}
        {sectionsVisible.work !== false && (data.work || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-white rounded-xl shadow-lg border-l-4 border-purple-600">
            <h2 className={`font-bold text-gray-900 mb-4 text-center ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.workExperience", { default: "工作经历" })}
            </h2>
            <div className="space-y-3">
              {(data.work || []).slice(0, 3).map((job: any, index: number) => (
                <div key={job.id || index} className="relative pl-4 border-l-2 border-purple-300">
                  <div className="absolute left-[-5px] top-2 w-2 h-2 bg-purple-500 rounded-full"></div>
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{job.position}</h3>
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{job.company}</p>
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(job.startDateYear, job.startDateMonth, job.endDateYear, job.endDateMonth)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education Stats */}
        {sectionsVisible.education !== false && (data.education || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-white rounded-xl shadow-lg border-l-4 border-pink-600">
            <h2 className={`font-bold text-gray-900 mb-4 text-center ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.education", { default: "教育背景" })}
            </h2>
            <div className="space-y-3">
              {(data.education || []).slice(0, 3).map((edu: any, index: number) => (
                <div key={edu.id || index} className="relative pl-4 border-l-2 border-pink-300">
                  <div className="absolute left-[-5px] top-2 w-2 h-2 bg-pink-500 rounded-full"></div>
                  <h3 className={`font-semibold text-gray-900 ${isExport ? 'text-sm' : 'text-base'}`}>{edu.school}</h3>
                  <p className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{edu.degree} {edu.field && `- ${edu.field}`}</p>
                  <p className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                    {formatDateRangeLocal(edu.startDateYear, edu.startDateMonth, edu.endDateYear, edu.endDateMonth)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Additional Sections Grid */}
      <div className={`grid gap-6 mb-6 ${isExport ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-2'}`}>
        {/* Projects */}
        {sectionsVisible.projects !== false && (data.projects || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.projects", { default: "项目经验" })}
            </h2>
            <div className="space-y-3">
              {(data.projects || []).slice(0, 3).map((project: any, index: number) => (
                <div key={index} className="flex items-center space-x-3">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{project.name}</div>
                    {project.startDate && project.endDate && (
                      <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>
                        {project.startDate} - {project.endDate}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Languages */}
        {sectionsVisible.languages !== false && (data.languages || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.languages", { default: "语言能力" })}
            </h2>
            <div className="space-y-3">
              {(data.languages || []).map((lang: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className={`font-medium text-gray-700 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.language}</span>
                  <span className={`text-purple-600 ${isExport ? 'text-sm' : 'text-base'}`}>{lang.proficiency}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Certifications */}
        {sectionsVisible.certifications !== false && (data.certifications || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-green-50 to-blue-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.certifications", { default: "证书" })}
            </h2>
            <div className="space-y-3">
              {(data.certifications || []).map((cert: any, index: number) => (
                <div key={index} className="flex items-center space-x-3">
                  <span className="w-2 h-2 bg-green-600 rounded-full"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{cert.name}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{cert.issuer}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Awards */}
        {sectionsVisible.awards !== false && (data.awards || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.awards", { default: "获奖荣誉" })}
            </h2>
            <div className="space-y-3">
              {(data.awards || []).map((award: any, index: number) => (
                <div key={index} className="flex items-center space-x-3">
                  <span className="w-2 h-2 bg-yellow-600 rounded-full"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{award.name}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{award.issuer}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Interests */}
        {sectionsVisible.interests !== false && (data.interests || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-pink-50 to-purple-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.interests", { default: "兴趣爱好" })}
            </h2>
            <div className="flex flex-wrap gap-2">
              {(data.interests || []).map((interest: any, index: number) => (
                <span key={interest.id || index} className={`px-3 py-1 bg-pink-200 text-pink-700 rounded-full ${isExport ? 'text-xs' : 'text-sm'}`}>
                  {interest.name || interest}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Volunteer Experience */}
        {sectionsVisible.volunteer !== false && (data.volunteer || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-green-50 to-teal-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.volunteerExperience", { default: "志愿服务" })}
            </h2>
            <div className="space-y-3">
              {(data.volunteer || []).map((vol: any, index: number) => (
                <div key={index} className="flex items-center space-x-3">
                  <span className="w-2 h-2 bg-green-600 rounded-full"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{vol.organization}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{vol.position}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Publications and References */}
      <div className={`grid gap-6 ${isExport ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-2'}`}>
        {/* Publications */}
        {sectionsVisible.publications !== false && (data.publications || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.publications", { default: "发表作品" })}
            </h2>
            <div className="space-y-3">
              {(data.publications || []).map((pub: any, index: number) => (
                <div key={index} className="flex items-start space-x-3">
                  <span className="w-2 h-2 bg-indigo-600 rounded-full mt-2"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{pub.title}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.publication}</div>
                    {pub.date && (
                      <div className={`text-gray-500 ${isExport ? 'text-xs' : 'text-sm'}`}>{pub.date}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* References */}
        {sectionsVisible.references !== false && (data.references || []).length > 0 && (
          <div className="pdf-section pdf-item p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl">
            <h2 className={`font-bold text-gray-900 mb-4 ${isExport ? 'text-base' : 'text-xl'}`}>
              {t("cvbuilder.references", { default: "推荐人" })}
            </h2>
            <div className="space-y-3">
              {(data.references || []).map((ref: any, index: number) => (
                <div key={index} className="flex items-start space-x-3">
                  <span className="w-2 h-2 bg-gray-600 rounded-full mt-2"></span>
                  <div>
                    <div className={`font-medium text-gray-800 ${isExport ? 'text-sm' : 'text-base'}`}>{ref.name}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.title} at {ref.company}</div>
                    <div className={`text-gray-600 ${isExport ? 'text-xs' : 'text-sm'}`}>{ref.email}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}