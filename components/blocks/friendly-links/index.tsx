import { useTranslations, useLocale } from "next-intl";

interface FriendlyLink {
  title: string;
  url: string;
  description?: string;
}

const friendlyLinks: FriendlyLink[] = [
  {
    title: "Password Generator",
    url: "https://generatepassword12.org/",
    description: "Generate secure passwords"
  },
  {
    title: "Name Generator", 
    url: "https://name-generator.app/",
    description: "Generate creative names"
  },
  {
    title: "Circle Crop Image",
    url: "https://circlecropimage.net/",
    description: "Crop images into circles"
  }
];

const friendlyLinksZh: FriendlyLink[] = [
  {
    title: "密码生成器",
    url: "https://generatepassword12.org/",
    description: "生成安全密码"
  },
  {
    title: "名字生成器",
    url: "https://name-generator.app/",
    description: "生成创意名字"
  },
  {
    title: "圆形裁剪图片",
    url: "https://circlecropimage.net/",
    description: "将图片裁剪成圆形"
  }
];

export default function FriendlyLinks({ className = "" }: { className?: string }) {
  const t = useTranslations();
  const locale = useLocale();

  // 根据当前语言选择链接
  const links = locale === 'zh' ? friendlyLinksZh : friendlyLinks;
  
  return (
    <div className={`py-12 bg-gray-50 border-t border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h3 className="text-xl font-bold text-gray-900 mb-3">
            {locale === 'zh' ? '🔗 实用工具推荐' : '🔗 Useful Tools'}
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {locale === 'zh'
              ? '发现更多实用的在线工具，提升您的工作效率'
              : 'Discover more useful online tools to boost your productivity'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {links.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block p-6 bg-white rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 group"
            >
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-xl font-bold">
                    {link.title.charAt(0)}
                  </span>
                </div>
                <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2">
                  {link.title}
                </h4>
                {link.description && (
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {link.description}
                  </p>
                )}
              </div>
            </a>
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-xs text-gray-400">
            {locale === 'zh'
              ? '友情链接 · 互相推荐优质工具'
              : 'Friendly Links · Mutual recommendation of quality tools'}
          </p>
        </div>
      </div>
    </div>
  );
}
