/**
 * Format date range based on locale
 * @param startYear - Start year as string
 * @param startMonth - Start month as string (01-12)
 * @param endYear - End year as string
 * @param endMonth - End month as string (01-12)
 * @param locale - Current locale ('en' or 'zh')
 * @param presentText - Text to show for current/present
 * @returns Formatted date range string
 */
export function formatDateRange(
  startYear?: string,
  startMonth?: string,
  endYear?: string,
  endMonth?: string,
  locale: string = 'zh',
  presentText: string = 'Present'
): string {
  if (locale === 'en') {
    // English format: "Mar 2021"
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const start = startYear && startMonth ? `${monthNames[parseInt(startMonth) - 1]} ${startYear}` : "";
    // If endYear is empty or "Present", show presentText
    const end = endYear && endYear !== "Present" && endMonth ? `${monthNames[parseInt(endMonth) - 1]} ${endYear}` : presentText;
    return start ? `${start} - ${end}` : "";
  } else {
    // Chinese format: "2021年03月"
    const start = startYear && startMonth ? `${startYear}年${startMonth}月` : "";
    // If endYear is empty or "Present", show presentText
    const end = endYear && endYear !== "Present" && endMonth ? `${endYear}年${endMonth}月` : presentText;
    return start ? `${start} - ${end}` : "";
  }
}

/**
 * Format single date based on locale
 * @param year - Year as string
 * @param month - Month as string (01-12)
 * @param locale - Current locale ('en' or 'zh')
 * @returns Formatted date string
 */
export function formatDate(
  year?: string, 
  month?: string,
  locale: string = 'zh'
): string {
  if (!year) return '';
  
  if (locale === 'en') {
    // English format: "Mar 2021" or "2021"
    if (month) {
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
    return year;
  } else {
    // Chinese format: "2021年03月" or "2021年"
    return month ? `${year}年${month}月` : `${year}年`;
  }
}
