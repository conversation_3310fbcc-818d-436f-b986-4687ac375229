import { useTranslations } from 'next-intl';

export type PageType = 'home' | 'templates' | 'editor' | 'blog' | 'signin' | 'template';

export interface SEOMetadata {
  title: string;
  description: string;
  keywords: string;
  openGraph?: {
    title: string;
    description: string;
    type: string;
    url?: string;
    images?: Array<{
      url: string;
      width: number;
      height: number;
      alt: string;
    }>;
  };
  twitter?: {
    card: string;
    title: string;
    description: string;
    images?: string[];
  };
}

export function getPageMetadata(
  pageType: PageType,
  t: any,
  templateName?: string,
  customTitle?: string
): SEOMetadata {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  const logoUrl = 'https://image.generatepassword12.org/cvbuilderfreelogo.jpg';

  // 获取页面特定的 metadata
  const pageMetadata = t.raw(`metadata.pages.${pageType}`) || {};
  
  // 如果是模板页面，生成动态的 metadata
  if (pageType === 'template' && templateName) {
    const templateTitle = customTitle || templateName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    return {
      title: `${templateTitle} - Professional CV Template | CV Builder Free`,
      description: `Create a professional resume with our ${templateTitle} template. ATS-friendly design, easy customization, and instant PDF export. Start building your perfect CV today!`,
      keywords: `${templateName} template, cv template, resume template, professional resume, ATS friendly, free cv builder, ${templateTitle.toLowerCase()}`,
      openGraph: {
        title: `${templateTitle} - Professional CV Template`,
        description: `Create a professional resume with our ${templateTitle} template. ATS-friendly and completely free.`,
        type: 'website',
        url: `${baseUrl}/cv-template/${templateName}`,
        images: [{
          url: logoUrl,
          width: 1200,
          height: 630,
          alt: `${templateTitle} CV Template Preview`
        }]
      },
      twitter: {
        card: 'summary_large_image',
        title: `${templateTitle} - Professional CV Template`,
        description: `Create a professional resume with our ${templateTitle} template. ATS-friendly and completely free.`,
        images: [logoUrl]
      }
    };
  }

  // 使用页面特定的 metadata 或回退到默认值
  const title = pageMetadata.title || t('metadata.title');
  const description = pageMetadata.description || t('metadata.description');
  const keywords = pageMetadata.keywords || t('metadata.keywords');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: baseUrl,
      images: [{
        url: logoUrl,
        width: 1200,
        height: 630,
        alt: 'CV Builder Free - Professional Resume Builder'
      }]
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [logoUrl]
    }
  };
}

export function generateStructuredData(pageType: PageType, templateName?: string) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://cvbuilder.com';
  
  const baseStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "CV Builder Free",
    "description": "Free online CV builder and resume maker with professional templates",
    "url": baseUrl,
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Organization",
      "name": "CV Builder Free",
      "url": baseUrl
    }
  };

  if (pageType === 'templates') {
    return {
      ...baseStructuredData,
      "@type": "CollectionPage",
      "name": "Professional CV Templates",
      "description": "Collection of professional CV templates for different industries"
    };
  }

  if (pageType === 'template' && templateName) {
    return {
      "@context": "https://schema.org",
      "@type": "CreativeWork",
      "name": `${templateName.replace(/-/g, ' ')} CV Template`,
      "description": `Professional CV template for creating ATS-friendly resumes`,
      "url": `${baseUrl}/cv-template/${templateName}`,
      "creator": {
        "@type": "Organization",
        "name": "CV Builder Free"
      },
      "isAccessibleForFree": true
    };
  }

  return baseStructuredData;
}
