"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import type { CVData } from "@/types/pages/cv-builder";

interface CVBuilderContextProps {
  data: CVData;
  setData: (data: CVData) => void;
  updateSection: <K extends keyof CVData>(section: K, value: CVData[K]) => void;
  reset: () => void;
  load: () => void;
  save: () => void;
  sectionsVisible: Record<string, boolean>;
  toggleSectionVisible: (section: string) => void;
  sectionOrder: string[];
  setSectionOrder: (order: string[]) => void;
}

const defaultData: CVData = {
  personal: {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    address: "San Francisco, CA",
    website: "sarahjohnson.dev",
    linkedin: "linkedin.com/in/sarah<PERSON>hnson",
    github: "github.com/sarah<PERSON><PERSON>son"
  },
  profile: {
    summary: "Experienced Software Engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable web applications and leading cross-functional teams. Passionate about creating innovative solutions that drive business growth and enhance user experience."
  },
  jobIntention: {
    position: "Senior Software Engineer",
    targetSalary: "$120,000 - $150,000",
    preferredLocation: "San Francisco Bay Area, CA",
    availability: "Available immediately",
    workType: "Full-time / Remote",
    description: "Seeking a challenging role where I can leverage my technical expertise and leadership skills to build innovative products and mentor junior developers."
  },
  work: [
    {
      id: "1",
      company: "TechCorp Inc.",
      position: "Software Engineer",
      startDateYear: "2021",
      startDateMonth: "03",
      endDateYear: "",
      endDateMonth: "",
      location: "San Francisco, CA",
      description: "• Led development of microservices architecture serving 1M+ daily users\n• Improved application performance by 40% through code optimization and caching strategies\n• Mentored 3 junior developers and conducted technical interviews\n• Collaborated with product managers and designers to deliver user-centric features"
    },
    {
      id: "2",
      company: "StartupXYZ",
      position: "Full Stack Developer",
      startDateYear: "2019",
      startDateMonth: "06",
      endDateYear: "2021",
      endDateMonth: "02",
      location: "Palo Alto, CA",
      description: "• Built responsive web applications using React, Node.js, and PostgreSQL\n• Implemented CI/CD pipelines reducing deployment time by 60%\n• Developed RESTful APIs and integrated third-party services\n• Participated in agile development process and sprint planning"
    },
    {
      id: "3",
      company: "Digital Solutions LLC",
      position: "Junior Developer",
      startDateYear: "2018",
      startDateMonth: "08",
      endDateYear: "2019",
      endDateMonth: "05",
      location: "San Jose, CA",
      description: "• Developed and maintained client websites using HTML, CSS, and JavaScript\n• Collaborated with design team to implement pixel-perfect UI components\n• Fixed bugs and implemented new features based on client feedback\n• Gained experience with version control systems and code review processes"
    }
  ],
  education: [
    {
      id: "1",
      school: "University of California, Berkeley",
      degree: "Bachelor of Science in Computer Science",
      startDateYear: "2014",
      startDateMonth: "09",
      endDateYear: "2018",
      endDateMonth: "05",
      location: "Berkeley, CA",
      description: "• GPA: 3.7/4.0\n• Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering\n• Dean's List: Fall 2016, Spring 2017\n• Senior Project: Built a machine learning-powered recommendation system"
    }
  ],
  projects: [
    {
      id: "1",
      name: "E-commerce Platform",
      description: "Built a full-stack e-commerce platform using React, Node.js, and MongoDB. Features include user authentication, payment processing, inventory management, and admin dashboard.",
      technologies: ["React", "Node.js", "MongoDB", "Stripe API", "AWS"],
      startDate: "2022-01",
      endDate: "2022-06",
      url: "github.com/sarahjohnson/ecommerce-platform"
    },
    {
      id: "2",
      name: "Task Management App",
      description: "Developed a collaborative task management application with real-time updates, file sharing, and team communication features.",
      technologies: ["Vue.js", "Express.js", "Socket.io", "PostgreSQL"],
      startDate: "2021-08",
      endDate: "2021-12",
      url: "github.com/sarahjohnson/task-manager"
    }
  ],
  skills: [
    { id: "1", name: "JavaScript", level: "Expert" },
    { id: "2", name: "React", level: "Expert" },
    { id: "3", name: "Node.js", level: "Advanced" },
    { id: "4", name: "Python", level: "Advanced" },
    { id: "5", name: "TypeScript", level: "Advanced" },
    { id: "6", name: "AWS", level: "Intermediate" },
    { id: "7", name: "Docker", level: "Intermediate" },
    { id: "8", name: "PostgreSQL", level: "Advanced" },
    { id: "9", name: "MongoDB", level: "Intermediate" },
    { id: "10", name: "Git", level: "Expert" }
  ],
  languages: [
    { id: "1", language: "English", proficiency: "Native" },
    { id: "2", language: "Spanish", proficiency: "Conversational" },
    { id: "3", language: "French", proficiency: "Basic" }
  ],
  certifications: [
    {
      id: "1",
      name: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      issueDate: "2022-09",
      issueDateYear: "2022",
      issueDateMonth: "09"
    },
    {
      id: "2",
      name: "Certified Scrum Master",
      issuer: "Scrum Alliance",
      issueDate: "2021-03",
      issueDateYear: "2021",
      issueDateMonth: "03"
    }
  ],
  volunteer: [
    {
      id: "1",
      organization: "Code for America",
      role: "Volunteer Developer",
      startDate: "2020-01",
      startDateYear: "2020",
      startDateMonth: "01",
      endDate: "Present",
      description: "Contribute to open-source projects that help government agencies better serve their communities. Developed web applications for local nonprofits and civic organizations."
    }
  ],
  publications: [
    {
      id: "1",
      title: "Building Scalable Web Applications with Microservices",
      authors: ["Sarah Johnson"],
      publication: "Tech Blog",
      date: "2022-11",
      dateYear: "2022",
      dateMonth: "11"
    }
  ],
  awards: [
    {
      id: "1",
      name: "Employee of the Year",
      issuer: "TechCorp Inc.",
      date: "2022-12",
      dateYear: "2022",
      dateMonth: "12",
      description: "Recognized for outstanding performance and leadership in delivering critical projects"
    },
    {
      id: "2",
      name: "Hackathon Winner",
      issuer: "Bay Area Tech Meetup",
      date: "2021-10",
      dateYear: "2021",
      dateMonth: "10",
      description: "First place in 48-hour hackathon for developing an AI-powered productivity tool"
    }
  ],
  interests: [
    { id: "1", name: "Open Source Contributing" },
    { id: "2", name: "Machine Learning" },
    { id: "3", name: "Rock Climbing" },
    { id: "4", name: "Photography" },
    { id: "5", name: "Cooking" }
  ],
  references: [
    {
      id: "1",
      name: "Michael Chen",
      title: "Senior Engineering Manager",
      company: "TechCorp Inc.",
      email: "<EMAIL>",
      phone: "+****************"
    },
    {
      id: "2",
      name: "Emily Rodriguez",
      title: "Product Manager",
      company: "StartupXYZ",
      email: "<EMAIL>",
      phone: "+****************"
    }
  ],
  templateSettings: {
    showSection: {
      profile: true,
      skills: true,
      projects: true,
      certifications: true,
      languages: true,
      volunteer: true,
      publications: true,
      awards: true,
      interests: true,
      references: true,
    },
    sectionOrder: ["personal", "profile", "jobIntention", "work", "education", "skills"],
  },
};

const defaultSectionsVisible = {
  personal: true,
  profile: true,
  jobIntention: true,
  work: true,
  education: true,
  skills: true,
  projects: true,
  languages: true,
  certifications: true,
  volunteer: true,
  publications: true,
  awards: true,
  interests: true,
  references: true,
};

const defaultSectionOrder = [
  "personal", 
  "profile", 
  "jobIntention", 
  "work", 
  "education", 
  "skills",
  "projects",
  "languages",
  "certifications",
  "volunteer",
  "publications",
  "awards",
  "interests",
  "references"
];

const CVBuilderContext = createContext<CVBuilderContextProps | undefined>(undefined);

export const useCVBuilder = () => {
  const ctx = useContext(CVBuilderContext);
  if (!ctx) throw new Error("useCVBuilder must be used within CVBuilderProvider");
  return ctx;
};

const STORAGE_KEY = "cvbuilder.data";
const STORAGE_SECTIONS_KEY = "cvbuilder.sectionsVisible";
const STORAGE_ORDER_KEY = "cvbuilder.sectionOrder";

export const CVBuilderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [data, setData] = useState<CVData>(defaultData);
  const [sectionsVisible, setSectionsVisible] = useState<Record<string, boolean>>(defaultSectionsVisible);
  const [sectionOrder, setSectionOrder] = useState<string[]>(defaultSectionOrder);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // 只在客户端挂载后从 localStorage 恢复数据
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      // 合并默认字段，防止老数据缺失新字段
      const parsed = JSON.parse(saved);

      // 数据迁移：修复 website 和 linkedin 字段混淆的问题
      let migratedPersonal = { ...defaultData.personal, ...parsed.personal };
      if (parsed.personal) {
        // 如果 website 字段包含 linkedin.com，则移动到 linkedin 字段
        if (parsed.personal.website && parsed.personal.website.includes('linkedin.com')) {
          migratedPersonal.linkedin = parsed.personal.website;
          migratedPersonal.website = defaultData.personal.website;
        }
        // 如果 linkedin 字段为空但 website 包含 linkedin，则进行修复
        if (!parsed.personal.linkedin && parsed.personal.website && parsed.personal.website.includes('linkedin.com')) {
          migratedPersonal.linkedin = parsed.personal.website;
          migratedPersonal.website = defaultData.personal.website;
        }
      }

      // 数据迁移：将旧的证书日期格式转换为新的年份和月份字段
      let migratedCertifications = (parsed.certifications || []).map((cert: any) => {
        const migratedCert = { ...cert };

        // 如果有旧的 issueDate 格式（如 "2022-09"），转换为年份和月份
        if (cert.issueDate && !cert.issueDateYear && !cert.issueDateMonth) {
          const [year, month] = cert.issueDate.split('-');
          if (year && month) {
            migratedCert.issueDateYear = year;
            migratedCert.issueDateMonth = month;
          }
        }

        // 如果有旧的 expiryDate 格式，转换为年份和月份
        if (cert.expiryDate && !cert.expiryDateYear && !cert.expiryDateMonth) {
          const [year, month] = cert.expiryDate.split('-');
          if (year && month) {
            migratedCert.expiryDateYear = year;
            migratedCert.expiryDateMonth = month;
          }
        }

        return migratedCert;
      });

      // 数据迁移：将志愿者经历的旧日期格式转换为新的年份和月份字段
      let migratedVolunteer = (parsed.volunteer || []).map((vol: any) => {
        const migratedVol = { ...vol };

        if (vol.startDate && !vol.startDateYear && !vol.startDateMonth) {
          const [year, month] = vol.startDate.split('-');
          if (year && month) {
            migratedVol.startDateYear = year;
            migratedVol.startDateMonth = month;
          }
        }

        if (vol.endDate && vol.endDate !== "Present" && !vol.endDateYear && !vol.endDateMonth) {
          const [year, month] = vol.endDate.split('-');
          if (year && month) {
            migratedVol.endDateYear = year;
            migratedVol.endDateMonth = month;
          }
        }

        return migratedVol;
      });

      // 数据迁移：将出版物的旧日期格式转换为新的年份和月份字段
      let migratedPublications = (parsed.publications || []).map((pub: any) => {
        const migratedPub = { ...pub };

        if (pub.date && !pub.dateYear && !pub.dateMonth) {
          const [year, month] = pub.date.split('-');
          if (year && month) {
            migratedPub.dateYear = year;
            migratedPub.dateMonth = month;
          }
        }

        return migratedPub;
      });

      // 数据迁移：将奖项的旧日期格式转换为新的年份和月份字段
      let migratedAwards = (parsed.awards || []).map((award: any) => {
        const migratedAward = { ...award };

        if (award.date && !award.dateYear && !award.dateMonth) {
          const [year, month] = award.date.split('-');
          if (year && month) {
            migratedAward.dateYear = year;
            migratedAward.dateMonth = month;
          }
        }

        return migratedAward;
      });

      setData({
        ...defaultData,
        ...parsed,
        personal: migratedPersonal,
        jobIntention: { ...defaultData.jobIntention, ...parsed.jobIntention },
        // 确保新字段有默认值
        projects: parsed.projects || [],
        languages: parsed.languages || [],
        certifications: migratedCertifications,
        volunteer: migratedVolunteer,
        publications: migratedPublications,
        awards: migratedAwards,
        interests: parsed.interests || [],
        references: parsed.references || [],
        templateSettings: {
          ...defaultData.templateSettings,
          ...parsed.templateSettings,
        }
      });
    }

    const savedSections = localStorage.getItem(STORAGE_SECTIONS_KEY);
    if (savedSections) setSectionsVisible(JSON.parse(savedSections));
    const savedOrder = localStorage.getItem(STORAGE_ORDER_KEY);
    if (savedOrder) setSectionOrder(JSON.parse(savedOrder));
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    }
  }, [data, mounted]);

  useEffect(() => {
    if (mounted) {
      localStorage.setItem(STORAGE_SECTIONS_KEY, JSON.stringify(sectionsVisible));
    }
  }, [sectionsVisible, mounted]);
  useEffect(() => {
    if (mounted) {
      localStorage.setItem(STORAGE_ORDER_KEY, JSON.stringify(sectionOrder));
    }
  }, [sectionOrder, mounted]);

  const updateSection = <K extends keyof CVData>(section: K, value: CVData[K]) => {
    console.log(`Updating section ${section}:`, value);
    console.log('Previous data state:', data);
    setData((prev) => {
      const updated = { ...prev, [section]: value };
      console.log('Updated data state:', updated);
      return updated;
    });
  };

  const reset = () => {
    setData(defaultData);
    setSectionsVisible(defaultSectionsVisible);
    setSectionOrder(defaultSectionOrder);
  };
  const load = () => {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) setData(JSON.parse(saved));
    const savedSections = localStorage.getItem(STORAGE_SECTIONS_KEY);
    if (savedSections) setSectionsVisible(JSON.parse(savedSections));
    const savedOrder = localStorage.getItem(STORAGE_ORDER_KEY);
    if (savedOrder) setSectionOrder(JSON.parse(savedOrder));
  };
  const save = () => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    localStorage.setItem(STORAGE_SECTIONS_KEY, JSON.stringify(sectionsVisible));
    localStorage.setItem(STORAGE_ORDER_KEY, JSON.stringify(sectionOrder));
  };
  const toggleSectionVisible = (section: string) => {
    setSectionsVisible((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  if (!mounted) return null;

  return (
    <CVBuilderContext.Provider value={{ data, setData, updateSection, reset, load, save, sectionsVisible, toggleSectionVisible, sectionOrder, setSectionOrder }}>
      {children}
    </CVBuilderContext.Provider>
  );
};