/* 屏幕和打印都生效的双列布局 */
.pdf-two-column {
  display: flex;
  width: 100%;
  min-height: 0;
}
.pdf-sidebar {
  width: 240px;
  min-width: 240px;
  max-width: 240px;
}
.pdf-main-content {
  flex: 1;
  min-width: 0;
}

/* PDF导出专用样式 */

/* 确保A4页面尺寸 */
@media print {
  @page {
    size: 794px 1123px;
    margin: 56px;
  }
  html, body {
    width: 794px !important;
    height: 1123px !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
  }
  .pdf-container {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 100% !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    background: white !important;
    padding: 20px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
    color: #000 !important;
  }
  .pdf-two-column {
    display: flex !important;
    width: 100% !important;
    min-height: 0 !important;
    gap: 20px !important;
  }
  .pdf-sidebar {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
    flex-shrink: 0 !important;
  }
  .pdf-main-content {
    flex: 1 !important;
    min-width: 0 !important;
  }
  .pdf-section, .pdf-item {
    margin-bottom: 12px !important;
  }
  .pdf-section:last-child, .pdf-item:last-child {
    margin-bottom: 0 !important;
  }
  .pdf-item {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
  /* 防止分页时内容被切断，仅保留特殊元素 */
  .page-break-avoid {
    /* 不再全局避免分页 */
  }

  /* 确保文字不被遮挡 */
  .pdf-safe-text {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    word-break: break-word !important;
  }

  /* 避免孤立的标题 */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid !important;
    break-after: avoid !important;
  }

  /* 避免在表格、图片等元素内分页 */
  table, img, figure {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
  
  /* 网格布局优化 */
  .pdf-grid {
    display: grid !important;
    grid-template-columns: 1fr 2fr !important;
    gap: 8mm !important;
    width: 100% !important;
  }
  
  /* 文字大小优化 */
  .pdf-title {
    font-size: 18px !important;
    line-height: 1.2 !important;
    margin-bottom: 8px !important;
  }
  
  .pdf-subtitle {
    font-size: 14px !important;
    line-height: 1.3 !important;
    margin-bottom: 6px !important;
  }
  
  .pdf-body-text {
    font-size: 12px !important;
    line-height: 1.4 !important;
    margin-bottom: 4px !important;
  }
  
  .pdf-small-text {
    font-size: 11px !important;
    line-height: 1.3 !important;
    margin-bottom: 3px !important;
  }
  
  .pdf-tiny-text {
    font-size: 10px !important;
    line-height: 1.2 !important;
    margin-bottom: 2px !important;
  }
  
  /* 间距优化 */
  .pdf-section {
    margin-bottom: 16px !important;
  }
  
  /* 确保背景色在PDF中显示 */
  .pdf-background {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* 文字大小类 */
  .text-xs { font-size: 10px !important; line-height: 1.2 !important; }
  .text-sm { font-size: 11px !important; line-height: 1.3 !important; }
  .text-base { font-size: 12px !important; line-height: 1.4 !important; }
  .text-lg { font-size: 14px !important; line-height: 1.3 !important; }
  .text-xl { font-size: 16px !important; line-height: 1.2 !important; }
  .text-2xl { font-size: 18px !important; line-height: 1.1 !important; }
  .text-3xl { font-size: 20px !important; line-height: 1.1 !important; }

  /* 间距类 */
  .mb-1 { margin-bottom: 4px !important; }
  .mb-2 { margin-bottom: 8px !important; }
  .mb-3 { margin-bottom: 12px !important; }
  .mb-4 { margin-bottom: 16px !important; }
  .mb-6 { margin-bottom: 24px !important; }
  .mb-8 { margin-bottom: 32px !important; }

  /* 网格布局 */
  .grid { display: grid !important; }
  .grid-cols-1 { grid-template-columns: 1fr !important; }
  .grid-cols-2 { grid-template-columns: 1fr 1fr !important; }
  .lg\\:grid-cols-3 { grid-template-columns: 1fr 1fr 1fr !important; }
  .lg\\:col-span-2 { grid-column: span 2 !important; }
  .gap-4 { gap: 16px !important; }
  .gap-6 { gap: 24px !important; }
  .gap-8 { gap: 32px !important; }

  /* 边框和背景 */
  .border-l-2 { border-left-width: 2px !important; border-left-style: solid !important; }
  .border-l-4 { border-left-width: 4px !important; border-left-style: solid !important; }
  .border-b { border-bottom-width: 1px !important; border-bottom-style: solid !important; }
  .border-b-2 { border-bottom-width: 2px !important; border-bottom-style: solid !important; }
  .border-gray-100 { border-color: #f3f4f6 !important; }
  .border-gray-200 { border-color: #e5e7eb !important; }
  .border-gray-300 { border-color: #d1d5db !important; }
  .border-blue-500 { border-color: #3b82f6 !important; }
  .border-green-500 { border-color: #10b981 !important; }
  .border-purple-500 { border-color: #8b5cf6 !important; }
  .border-orange-500 { border-color: #f97316 !important; }
  .border-teal-500 { border-color: #14b8a6 !important; }
  .border-yellow-500 { border-color: #eab308 !important; }
  .border-indigo-500 { border-color: #6366f1 !important; }
  .bg-white { background-color: white !important; }
  .bg-gray-200 { background-color: #e5e7eb !important; }
  .bg-blue-500 { background-color: #3b82f6 !important; }

  /* 文字颜色 */
  .text-gray-900 { color: #111827 !important; }
  .text-gray-800 { color: #1f2937 !important; }
  .text-gray-700 { color: #374151 !important; }
  .text-gray-600 { color: #4b5563 !important; }
  .text-gray-500 { color: #6b7280 !important; }
  .text-blue-600 { color: #2563eb !important; }

  /* 字体权重 */
  .font-bold { font-weight: 700 !important; }
  .font-semibold { font-weight: 600 !important; }
  .font-medium { font-weight: 500 !important; }

  /* 布局类 */
  .flex { display: flex !important; }
  .flex-wrap { flex-wrap: wrap !important; }
  .justify-between { justify-content: space-between !important; }
  .justify-center { justify-content: center !important; }
  .items-start { align-items: flex-start !important; }
  .items-center { align-items: center !important; }
  .text-center { text-align: center !important; }
  .text-right { text-align: right !important; }
  .text-justify { text-align: justify !important; }

  /* 间距和填充 */
  .space-x-6 > * + * { margin-left: 24px !important; }
  .gap-1 { gap: 4px !important; }
  .pl-2 { padding-left: 8px !important; }
  .pl-3 { padding-left: 12px !important; }
  .pl-4 { padding-left: 16px !important; }
  .pb-1 { padding-bottom: 4px !important; }
  .pb-2 { padding-bottom: 8px !important; }
  .px-1 { padding-left: 4px !important; padding-right: 4px !important; }
  .py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  .py-2 { padding-top: 8px !important; padding-bottom: 8px !important; }

  /* 其他样式 */
  .leading-relaxed { line-height: 1.625 !important; }
  .rounded-full { border-radius: 9999px !important; }
  .h-1\\.5 { height: 6px !important; }
  .h-2 { height: 8px !important; }
  .w-full { width: 100% !important; }
  .whitespace-pre-line { white-space: pre-line !important; }

  /* PDF 头像样式 */
  .pdf-avatar {
    width: 64px !important;
    height: 64px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    display: block !important;
    margin-bottom: 8px !important;
    background: #e0e7ff !important;
    color: #2563eb !important;
    font-weight: bold !important;
    font-size: 28px !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }
}

/* 通用PDF优化类 */
.pdf-optimized {
  width: 794px !important; /* A4 width at 96dpi */
  min-height: auto !important;
  padding: 40px !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  background: white !important;
  color: #000 !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
  print-color-adjust: exact !important;
  overflow: visible !important;
}

.pdf-optimized * {
  box-sizing: border-box !important;
}

.pdf-optimized .safe-text {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  word-break: break-word !important;
}

/* 特殊的PDF分页控制 */
/* @media screen 下的分页控制全部移除 */
