# CV Builder

Free online resume builder with drag-and-drop editor, real-time preview, and PDF export.

![preview](preview.png)

## Features

- 🎨 **Drag & Drop Editor** - Intuitive interface for building professional resumes
- 👀 **Real-time Preview** - See your changes instantly as you edit
- 📄 **PDF Export** - Download your resume as a professional PDF
- 🎯 **ATS Friendly** - Optimized for Applicant Tracking Systems
- 🌍 **Multilingual** - Support for English and Chinese
- 🆓 **Free to Use** - No registration required
- 📱 **Responsive** - Works perfectly on desktop and mobile

## Quick Start

1. Clone the repository

```bash
git clone https://github.com/maysunyoung/cvbuilder.git
```

2. Install dependencies

```bash
pnpm install
```

3. Run the development server

```bash
pnpm dev
```

4. Open [http://localhost:3001](http://localhost:3001) in your browser

## Customize

- Set your environment variables

```bash
cp .env.example .env.local
```

- Set your theme in `app/theme.css`

[shadcn-ui-theme-generator](https://zippystarter.com/tools/shadcn-ui-theme-generator)

- Set your landing page content in `i18n/pages/landing`

- Set your i18n messages in `i18n/messages`

## Deploy

- Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fmaysunyoung%2Fcvbuilder&project-name=cv-builder&repository-name=cv-builder&redirect-url=https%3A%2F%2Fcvbuilder.com&demo-title=CV%20Builder&demo-description=Free%20online%20resume%20builder%20with%20drag-and-drop%20editor&demo-url=https%3A%2F%2Fcvbuilder.com&demo-image=https%3A%2F%2Fcvbuilder.com%2Fpreview.png)

- Deploy to Cloudflare

1. Customize your environment variables

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

edit your environment variables in `.env.production`

and put all the environment variables under `[vars]` in `wrangler.toml`

2. Deploy

```bash
npm run cf:deploy
```

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn UI
- **Authentication**: NextAuth.js
- **Internationalization**: next-intl
- **Database**: Supabase
- **Payments**: Stripe
- **Deployment**: Vercel / Cloudflare Pages

## License

MIT License - see [LICENSE](LICENSE) for details
