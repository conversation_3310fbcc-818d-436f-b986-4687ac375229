export interface CVPersonalInfo {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  avatar?: string;
  website?: string;
  linkedin?: string;
  github?: string;
  title?: string;
  summary?: string;
  dateOfBirth?: string;
  nationality?: string;
  languages?: string[];
}

export interface CVProfile {
  summary: string;
  objective?: string;
  highlights?: string[];
  achievements?: string[];
}

export interface CVJobIntention {
  position: string;
  targetSalary?: string;
  preferredLocation?: string;
  availability?: string;
  workType?: string;
  description?: string;
}

export interface CVWorkExperience {
  id: string;
  company: string;
  position: string;
  startDateYear?: string;
  startDateMonth?: string;
  endDateYear?: string;
  endDateMonth?: string;
  description?: string;
  location?: string;
  achievements?: string[];
  technologies?: string[];
  responsibilities?: string[];
  projects?: string[];
  teamSize?: string;
  industry?: string;
}

export interface CVEducation {
  id: string;
  school: string;
  degree: string;
  field?: string;
  startDateYear?: string;
  startDateMonth?: string;
  endDateYear?: string;
  endDateMonth?: string;
  description?: string;
  location?: string;
  gpa?: string;
  honors?: string[];
  relevantCourses?: string[];
  thesis?: string;
  activities?: string[];
}

export interface CVSkill {
  id: string;
  name: string;
  level?: string;
  category?: string;
  yearsOfExperience?: string;
  proficiency?: string;
}

export interface CVProject {
  id: string;
  name: string;
  description: string;
  startDate?: string;
  endDate?: string;
  technologies?: string[];
  role?: string;
  teamSize?: string;
  achievements?: string[];
  url?: string;
  image?: string;
}

export interface CVCertification {
  id: string;
  name: string;
  issuer: string;
  issueDate?: string;
  expiryDate?: string;
  issueDateYear?: string;
  issueDateMonth?: string;
  expiryDateYear?: string;
  expiryDateMonth?: string;
  credentialId?: string;
  url?: string;
}

export interface CVLanguage {
  id: string;
  language: string;
  proficiency: string;
  level?: string;
}

export interface CVInterest {
  id: string;
  name: string;
  description?: string;
  category?: string;
}

export interface CVVolunteer {
  id: string;
  organization: string;
  role: string;
  startDate?: string;
  endDate?: string;
  startDateYear?: string;
  startDateMonth?: string;
  endDateYear?: string;
  endDateMonth?: string;
  description?: string;
  achievements?: string[];
}

export interface CVPublication {
  id: string;
  title: string;
  authors: string[];
  publication: string;
  date?: string;
  dateYear?: string;
  dateMonth?: string;
  url?: string;
  type?: string;
}

export interface CVAward {
  id: string;
  name: string;
  issuer: string;
  date?: string;
  dateYear?: string;
  dateMonth?: string;
  description?: string;
  category?: string;
}

export interface CVReference {
  id: string;
  name: string;
  title: string;
  company: string;
  email?: string;
  phone?: string;
  relationship?: string;
}

export interface CVData {
  personal: CVPersonalInfo;
  profile: CVProfile;
  jobIntention: CVJobIntention;
  work: CVWorkExperience[];
  education: CVEducation[];
  projects: CVProject[];
  skills: CVSkill[];
  languages: CVLanguage[];
  certifications: CVCertification[];
  volunteer: CVVolunteer[];
  publications: CVPublication[];
  awards: CVAward[];
  interests: CVInterest[];
  references: CVReference[];
  theme?: string;
  font?: string;
  colorScheme?: string;
  layout?: string;
  templateSettings?: {
    showSection?: {
      profile?: boolean;
      skills?: boolean;
      projects?: boolean;
      certifications?: boolean;
      languages?: boolean;
      volunteer?: boolean;
      publications?: boolean;
      awards?: boolean;
      interests?: boolean;
      references?: boolean;
    };
    sectionOrder?: string[];
    customFields?: Record<string, any>;
  };
}

export type ResumeData = CVData;