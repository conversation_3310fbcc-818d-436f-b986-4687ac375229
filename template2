
## 🎯 **组合式简历模板生成计划**

### **设计风格 × 布局结构 × 行业/人群 = 多样化模板**

---

## �� **第一阶段：核心组合模板（20个）**

### **1. 现代风格组合**
- `ModernTechTwoColumn.tsx` - 现代科技双栏（IT/开发）
- `ModernStartupSingle.tsx` - 现代创业单栏（初创公司）
- `ModernProductGrid.tsx` - 现代产品网格（产品经理）

### **2. 经典风格组合**
- `ClassicFinanceSingle.tsx` - 经典金融单栏（金融/银行）
- `ClassicLawTwoColumn.tsx` - 经典法律双栏（法律/咨询）
- `ClassicCorporateTimeline.tsx` - 经典企业时间轴（传统企业）

### **3. 极简风格组合**
- `MinimalAcademicSingle.tsx` - 极简学术单栏（学术/研究）
- `MinimalInternationalGrid.tsx` - 极简国际网格（海外求职）
- `MinimalCleanTimeline.tsx` - 极简清洁时间轴（通用）

### **4. 创意风格组合**
- `CreativeDesignerPortfolio.tsx` - 创意设计师作品集（UI/UX）
- `CreativeArtistGrid.tsx` - 创意艺术家网格（艺术/媒体）
- `CreativeMarketingInfographic.tsx` - 创意营销信息图（市场营销）

### **5. 优雅风格组合**
- `ElegantLuxurySingle.tsx` - 优雅奢侈品单栏（高端服务）
- `ElegantFashionTwoColumn.tsx` - 优雅时尚双栏（时尚/奢侈品）
- `ElegantConsultingTimeline.tsx` - 优雅咨询时间轴（咨询/顾问）

### **6. 科技感风格组合**
- `TechAIGrid.tsx` - AI科技网格（AI/区块链）
- `TechGamingInfographic.tsx` - 游戏科技信息图（游戏行业）
- `TechStartupTwoColumn.tsx` - 科技创业双栏（科技创业）

### **7. 混合风格组合**
- `HybridUXDesigner.tsx` - 混合UX设计师（UX/UI）
- `HybridBrandManager.tsx` - 混合品牌经理（品牌管理）
- `HybridCreativeTech.tsx` - 混合创意科技（创意技术）

---

## �� **第二阶段：特殊组合模板（15个）**

### **按内容重点组合**
- `SkillsFirstDeveloper.tsx` - 技能优先开发者（技术岗）
- `ExperienceFirstExecutive.tsx` - 经验优先高管（管理层）
- `EducationFirstStudent.tsx` - 教育优先学生（应届生）
- `PortfolioFirstDesigner.tsx` - 作品集优先设计师（设计师）

### **按特定行业组合**
- `HealthcareDoctor.tsx` - 医疗医生（医生/护士）
- `EducationTeacher.tsx` - 教育教师（教师/教育）
- `SalesBusiness.tsx` - 销售商务（销售/商务）
- `FreelancerCreative.tsx` - 自由职业创意（自由职业者）

### **按职业阶段组合**
- `StudentGraduate.tsx` - 应届毕业生（学生）
- `MidCareerProfessional.tsx` - 中职业专业人士（中高级）
- `SeniorExecutive.tsx` - 资深高管（高管）
- `CareerChanger.tsx` - 职业转换者（转行）

### **按特殊需求组合**
- `InternationalJobSeeker.tsx` - 国际求职者（海外求职）
- `RemoteWorker.tsx` - 远程工作者（远程工作）
- `EntrepreneurFounder.tsx` - 创业者创始人（创业）

---

## �� **第三阶段：高级组合模板（10个）**

### **多维度组合**
- `ModernTechSkillsFirst.tsx` - 现代科技技能优先
- `ClassicFinanceExperienceFirst.tsx` - 经典金融经验优先
- `CreativeDesignerPortfolioFirst.tsx` - 创意设计师作品集优先
- `ElegantLuxuryEducationFirst.tsx` - 优雅奢侈品教育优先

### **特殊场景组合**
- `TechStartupFounder.tsx` - 科技创业创始人
- `CreativeAgencyDirector.tsx` - 创意机构总监
- `AcademicResearchProfessor.tsx` - 学术研究教授
- `ConsultingPartner.tsx` - 咨询合伙人

### **新兴行业组合**
- `AIMachineLearning.tsx` - AI机器学习
- `BlockchainDeveloper.tsx` - 区块链开发
- `SustainabilityConsultant.tsx` - 可持续发展咨询

---

## 🎯 **生成策略**

1. **组合命名规则**：`[设计风格][布局结构][行业/人群].tsx`
2. **可复用组件**：创建通用的布局组件、样式组件
3. **低耦合设计**：每个模板独立，减少依赖
4. **最佳实践**：统一使用 `pdf-container pdf-optimized` 和 `pdf-section pdf-item` 类

**总计：45个不同组合的简历模板**
